diff --git a/src/js/components/DataTable/DataTable.js b/src/js/components/DataTable/DataTable.js
index 6c02fd954f2..46dfc39560e 100644
--- a/src/js/components/DataTable/DataTable.js
+++ b/src/js/components/DataTable/DataTable.js
@@ -108,6 +108,9 @@ const DataTable = ({
 
   // which column we are sorting on, with direction
   const [sort, setSort] = useState(sortProp || {});
+  useEffect(() => {
+    if (sortProp) setSort(sortProp);
+  }, [sortProp]);
 
   // the data filtered and sorted, if needed
   // Note: onUpdate mode expects the data to be passed
diff --git a/src/js/components/DataTable/__tests__/DataTable-test.js b/src/js/components/DataTable/__tests__/DataTable-test.js
index 65c0d6fec46..b87b321b309 100644
--- a/src/js/components/DataTable/__tests__/DataTable-test.js
+++ b/src/js/components/DataTable/__tests__/DataTable-test.js
@@ -1,9 +1,10 @@
 import React from 'react';
 import 'jest-styled-components';
-import { render, fireEvent } from '@testing-library/react';
+import { render, fireEvent, screen } from '@testing-library/react';
 
 import { Grommet } from '../../Grommet';
 import { Box } from '../../Box';
+import { Button } from '../../Button';
 import { Text } from '../../Text';
 import { DataTable } from '..';
 
@@ -272,6 +273,43 @@ describe('DataTable', () => {
     expect(container.firstChild).toMatchSnapshot();
   });
 
+  test('sort controlled', () => {
+    const Test = () => {
+      const [sort, setSort] = React.useState({
+        property: 'a',
+        direction: 'asc',
+      });
+
+      return (
+        <Grommet>
+          <Button
+            label="Sort data"
+            onClick={() => setSort({ property: 'a', direction: 'desc' })}
+          />
+          <DataTable
+            columns={[
+              { property: 'a', header: 'A' },
+              { property: 'b', header: 'B' },
+            ]}
+            data={[
+              { a: 'zero', b: 0 },
+              { a: 'one', b: 1 },
+              { a: 'two', b: 2 },
+            ]}
+            sort={sort}
+          />
+        </Grommet>
+      );
+    };
+    const { asFragment } = render(<Test />);
+    expect(asFragment()).toMatchSnapshot();
+
+    const sortButton = screen.getByRole('button', { name: 'Sort data' });
+    fireEvent.click(sortButton);
+
+    expect(asFragment()).toMatchSnapshot();
+  });
+
   test('sort nested object', () => {
     const { container, getByText } = render(
       <Grommet>
diff --git a/src/js/components/DataTable/__tests__/__snapshots__/DataTable-test.js.snap b/src/js/components/DataTable/__tests__/__snapshots__/DataTable-test.js.snap
index f08c9409f03..acc375f3904 100644
--- a/src/js/components/DataTable/__tests__/__snapshots__/DataTable-test.js.snap
+++ b/src/js/components/DataTable/__tests__/__snapshots__/DataTable-test.js.snap
@@ -2930,12 +2930,6 @@ exports[`DataTable custom theme 1`] = `
   border-radius: 4px;
 }
 
-.c10 {
-  font-size: 18px;
-  line-height: 24px;
-  font-weight: bold;
-}
-
 .c11 {
   position: relative;
 }
@@ -2954,6 +2948,12 @@ exports[`DataTable custom theme 1`] = `
   transform: translateY(-50%);
 }
 
+.c10 {
+  font-size: 18px;
+  line-height: 24px;
+  font-weight: bold;
+}
+
 .c7 {
   display: inline-block;
   box-sizing: border-box;
@@ -18333,17 +18333,6 @@ exports[`DataTable resizeable 1`] = `
   width: 12px;
 }
 
-.c6 {
-  font-size: 18px;
-  line-height: 24px;
-}
-
-.c18 {
-  font-size: 18px;
-  line-height: 24px;
-  font-weight: bold;
-}
-
 .c8 {
   position: relative;
 }
@@ -18362,6 +18351,17 @@ exports[`DataTable resizeable 1`] = `
   transform: translateY(-50%);
 }
 
+.c6 {
+  font-size: 18px;
+  line-height: 24px;
+}
+
+.c18 {
+  font-size: 18px;
+  line-height: 24px;
+  font-weight: bold;
+}
+
 .c3 {
   margin: 0;
   padding: 0;
@@ -36471,6 +36471,966 @@ exports[`DataTable sort 1`] = `
 </div>
 `;
 
+exports[`DataTable sort controlled 1`] = `
+<DocumentFragment>
+  .c11 {
+  display: inline-block;
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+  width: 24px;
+  height: 24px;
+  fill: #666666;
+  stroke: #666666;
+}
+
+.c11 g {
+  fill: inherit;
+  stroke: inherit;
+}
+
+.c11 *:not([stroke])[fill="none"] {
+  stroke-width: 0;
+}
+
+.c11 *[stroke*="#"],
+.c11 *[STROKE*="#"] {
+  stroke: inherit;
+  fill: none;
+}
+
+.c11 *[fill-rule],
+.c11 *[FILL-RULE],
+.c11 *[fill*="#"],
+.c11 *[FILL*="#"] {
+  fill: inherit;
+  stroke: none;
+}
+
+.c0 {
+  font-size: 18px;
+  line-height: 24px;
+  box-sizing: border-box;
+  -webkit-text-size-adjust: 100%;
+  -ms-text-size-adjust: 100%;
+  -moz-osx-font-smoothing: grayscale;
+  -webkit-font-smoothing: antialiased;
+}
+
+.c5 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: column;
+  -ms-flex-direction: column;
+  flex-direction: column;
+  -webkit-flex: 1 0 auto;
+  -ms-flex: 1 0 auto;
+  flex: 1 0 auto;
+}
+
+.c8 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  -webkit-align-items: center;
+  -webkit-box-align: center;
+  -ms-flex-align: center;
+  align-items: center;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: row;
+  -ms-flex-direction: row;
+  flex-direction: row;
+}
+
+.c14 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: column;
+  -ms-flex-direction: column;
+  flex-direction: column;
+}
+
+.c10 {
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+  -webkit-align-self: stretch;
+  -ms-flex-item-align: stretch;
+  align-self: stretch;
+  width: 6px;
+}
+
+.c9 {
+  font-size: 18px;
+  line-height: 24px;
+}
+
+.c15 {
+  font-size: 18px;
+  line-height: 24px;
+  font-weight: bold;
+}
+
+.c1 {
+  display: inline-block;
+  box-sizing: border-box;
+  cursor: pointer;
+  font: inherit;
+  -webkit-text-decoration: none;
+  text-decoration: none;
+  margin: 0;
+  background: transparent;
+  overflow: visible;
+  text-transform: none;
+  border: 2px solid #7D4CDB;
+  border-radius: 18px;
+  color: #444444;
+  padding: 4px 22px;
+  font-size: 18px;
+  line-height: 24px;
+  -webkit-transition-property: color,background-color,border-color,box-shadow;
+  transition-property: color,background-color,border-color,box-shadow;
+  -webkit-transition-duration: 0.1s;
+  transition-duration: 0.1s;
+  -webkit-transition-timing-function: ease-in-out;
+  transition-timing-function: ease-in-out;
+}
+
+.c1:hover {
+  box-shadow: 0px 0px 0px 2px #7D4CDB;
+}
+
+.c1:focus {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c1:focus > circle,
+.c1:focus > ellipse,
+.c1:focus > line,
+.c1:focus > path,
+.c1:focus > polygon,
+.c1:focus > polyline,
+.c1:focus > rect {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c1:focus::-moz-focus-inner {
+  border: 0;
+}
+
+.c1:focus:not(:focus-visible) {
+  outline: none;
+  box-shadow: none;
+}
+
+.c1:focus:not(:focus-visible) > circle,
+.c1:focus:not(:focus-visible) > ellipse,
+.c1:focus:not(:focus-visible) > line,
+.c1:focus:not(:focus-visible) > path,
+.c1:focus:not(:focus-visible) > polygon,
+.c1:focus:not(:focus-visible) > polyline,
+.c1:focus:not(:focus-visible) > rect {
+  outline: none;
+  box-shadow: none;
+}
+
+.c1:focus:not(:focus-visible)::-moz-focus-inner {
+  border: 0;
+}
+
+.c6 {
+  display: inline-block;
+  box-sizing: border-box;
+  cursor: pointer;
+  font: inherit;
+  -webkit-text-decoration: none;
+  text-decoration: none;
+  margin: 0;
+  background: transparent;
+  overflow: visible;
+  text-transform: none;
+  color: inherit;
+  outline: none;
+  border: none;
+  padding: 0;
+  text-align: inherit;
+  height: 100%;
+}
+
+.c6:focus {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c6:focus > circle,
+.c6:focus > ellipse,
+.c6:focus > line,
+.c6:focus > path,
+.c6:focus > polygon,
+.c6:focus > polyline,
+.c6:focus > rect {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c6:focus::-moz-focus-inner {
+  border: 0;
+}
+
+.c6:focus:not(:focus-visible) {
+  outline: none;
+  box-shadow: none;
+}
+
+.c6:focus:not(:focus-visible) > circle,
+.c6:focus:not(:focus-visible) > ellipse,
+.c6:focus:not(:focus-visible) > line,
+.c6:focus:not(:focus-visible) > path,
+.c6:focus:not(:focus-visible) > polygon,
+.c6:focus:not(:focus-visible) > polyline,
+.c6:focus:not(:focus-visible) > rect {
+  outline: none;
+  box-shadow: none;
+}
+
+.c6:focus:not(:focus-visible)::-moz-focus-inner {
+  border: 0;
+}
+
+.c4 {
+  margin: 0;
+  padding: 0;
+  font-weight: inherit;
+  text-align: inherit;
+  text-align: start;
+  border-bottom: solid 1px rgba(0,0,0,0.33);
+  padding: 0px;
+}
+
+.c13 {
+  margin: 0;
+  padding: 0;
+  font-weight: inherit;
+  text-align: inherit;
+  text-align: start;
+  padding-left: 12px;
+  padding-right: 12px;
+  padding-top: 6px;
+  padding-bottom: 6px;
+}
+
+.c2 {
+  border-spacing: 0;
+  border-collapse: collapse;
+  width: inherit;
+}
+
+.c3 {
+  position: relative;
+  border-spacing: 0;
+  border-collapse: separate;
+  height: 100%;
+}
+
+.c12:focus {
+  outline: 2px solid #6FFFB0;
+}
+
+.c12:focus:not(:focus-visible) {
+  outline: none;
+}
+
+.c7 {
+  padding: 6px 12px;
+}
+
+@media only screen and (max-width:768px) {
+  .c10 {
+    width: 3px;
+  }
+}
+
+<div
+    class="c0"
+  >
+    <button
+      class="c1"
+      type="button"
+    >
+      Sort data
+    </button>
+    <table
+      class="c2 c3"
+    >
+      <thead
+        class="StyledTable__StyledTableHeader-sc-1m3u5g-4 StyledDataTable__StyledDataTableHeader-sc-xrlyjm-4"
+      >
+        <tr
+          class="StyledTable__StyledTableRow-sc-1m3u5g-2 "
+        >
+          <th
+            class="c4 "
+            scope="col"
+          >
+            <div
+              class="c5"
+            >
+              <button
+                class="c6 c7"
+                type="button"
+              >
+                <div
+                  class="c8"
+                >
+                  <span
+                    class="c9"
+                  >
+                    A
+                  </span>
+                  <div
+                    class="c10"
+                  />
+                  <svg
+                    aria-label="FormUp"
+                    class="c11"
+                    viewBox="0 0 24 24"
+                  >
+                    <path
+                      d="m18 15-6-6-6 6"
+                      fill="none"
+                      stroke="#000"
+                      stroke-width="2"
+                    />
+                  </svg>
+                </div>
+              </button>
+            </div>
+          </th>
+          <th
+            class="c4 "
+            scope="col"
+          >
+            <div
+              class="c5"
+            >
+              <button
+                class="c6 c7"
+                type="button"
+              >
+                <div
+                  class="c8"
+                >
+                  <span
+                    class="c9"
+                  >
+                    B
+                  </span>
+                </div>
+              </button>
+            </div>
+          </th>
+        </tr>
+      </thead>
+      <tbody
+        class="StyledTable__StyledTableBody-sc-1m3u5g-3 c12"
+      >
+        <tr
+          class="StyledTable__StyledTableRow-sc-1m3u5g-2 "
+        >
+          <th
+            class="c13 "
+            scope="row"
+          >
+            <div
+              class="c14"
+            >
+              <span
+                class="c15"
+              >
+                one
+              </span>
+            </div>
+          </th>
+          <td
+            class="c13 "
+          >
+            <div
+              class="c14"
+            >
+              <span
+                class="c9"
+              >
+                1
+              </span>
+            </div>
+          </td>
+        </tr>
+        <tr
+          class="StyledTable__StyledTableRow-sc-1m3u5g-2 "
+        >
+          <th
+            class="c13 "
+            scope="row"
+          >
+            <div
+              class="c14"
+            >
+              <span
+                class="c15"
+              >
+                two
+              </span>
+            </div>
+          </th>
+          <td
+            class="c13 "
+          >
+            <div
+              class="c14"
+            >
+              <span
+                class="c9"
+              >
+                2
+              </span>
+            </div>
+          </td>
+        </tr>
+        <tr
+          class="StyledTable__StyledTableRow-sc-1m3u5g-2 "
+        >
+          <th
+            class="c13 "
+            scope="row"
+          >
+            <div
+              class="c14"
+            >
+              <span
+                class="c15"
+              >
+                zero
+              </span>
+            </div>
+          </th>
+          <td
+            class="c13 "
+          >
+            <div
+              class="c14"
+            >
+              <span
+                class="c9"
+              >
+                0
+              </span>
+            </div>
+          </td>
+        </tr>
+      </tbody>
+    </table>
+  </div>
+</DocumentFragment>
+`;
+
+exports[`DataTable sort controlled 2`] = `
+<DocumentFragment>
+  .c11 {
+  display: inline-block;
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+  width: 24px;
+  height: 24px;
+  fill: #666666;
+  stroke: #666666;
+}
+
+.c11 g {
+  fill: inherit;
+  stroke: inherit;
+}
+
+.c11 *:not([stroke])[fill="none"] {
+  stroke-width: 0;
+}
+
+.c11 *[stroke*="#"],
+.c11 *[STROKE*="#"] {
+  stroke: inherit;
+  fill: none;
+}
+
+.c11 *[fill-rule],
+.c11 *[FILL-RULE],
+.c11 *[fill*="#"],
+.c11 *[FILL*="#"] {
+  fill: inherit;
+  stroke: none;
+}
+
+.c0 {
+  font-size: 18px;
+  line-height: 24px;
+  box-sizing: border-box;
+  -webkit-text-size-adjust: 100%;
+  -ms-text-size-adjust: 100%;
+  -moz-osx-font-smoothing: grayscale;
+  -webkit-font-smoothing: antialiased;
+}
+
+.c5 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: column;
+  -ms-flex-direction: column;
+  flex-direction: column;
+  -webkit-flex: 1 0 auto;
+  -ms-flex: 1 0 auto;
+  flex: 1 0 auto;
+}
+
+.c8 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  -webkit-align-items: center;
+  -webkit-box-align: center;
+  -ms-flex-align: center;
+  align-items: center;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: row;
+  -ms-flex-direction: row;
+  flex-direction: row;
+}
+
+.c14 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: column;
+  -ms-flex-direction: column;
+  flex-direction: column;
+}
+
+.c10 {
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+  -webkit-align-self: stretch;
+  -ms-flex-item-align: stretch;
+  align-self: stretch;
+  width: 6px;
+}
+
+.c9 {
+  font-size: 18px;
+  line-height: 24px;
+}
+
+.c15 {
+  font-size: 18px;
+  line-height: 24px;
+  font-weight: bold;
+}
+
+.c1 {
+  display: inline-block;
+  box-sizing: border-box;
+  cursor: pointer;
+  font: inherit;
+  -webkit-text-decoration: none;
+  text-decoration: none;
+  margin: 0;
+  background: transparent;
+  overflow: visible;
+  text-transform: none;
+  border: 2px solid #7D4CDB;
+  border-radius: 18px;
+  color: #444444;
+  padding: 4px 22px;
+  font-size: 18px;
+  line-height: 24px;
+  -webkit-transition-property: color,background-color,border-color,box-shadow;
+  transition-property: color,background-color,border-color,box-shadow;
+  -webkit-transition-duration: 0.1s;
+  transition-duration: 0.1s;
+  -webkit-transition-timing-function: ease-in-out;
+  transition-timing-function: ease-in-out;
+}
+
+.c1:hover {
+  box-shadow: 0px 0px 0px 2px #7D4CDB;
+}
+
+.c1:focus {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c1:focus > circle,
+.c1:focus > ellipse,
+.c1:focus > line,
+.c1:focus > path,
+.c1:focus > polygon,
+.c1:focus > polyline,
+.c1:focus > rect {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c1:focus::-moz-focus-inner {
+  border: 0;
+}
+
+.c1:focus:not(:focus-visible) {
+  outline: none;
+  box-shadow: none;
+}
+
+.c1:focus:not(:focus-visible) > circle,
+.c1:focus:not(:focus-visible) > ellipse,
+.c1:focus:not(:focus-visible) > line,
+.c1:focus:not(:focus-visible) > path,
+.c1:focus:not(:focus-visible) > polygon,
+.c1:focus:not(:focus-visible) > polyline,
+.c1:focus:not(:focus-visible) > rect {
+  outline: none;
+  box-shadow: none;
+}
+
+.c1:focus:not(:focus-visible)::-moz-focus-inner {
+  border: 0;
+}
+
+.c6 {
+  display: inline-block;
+  box-sizing: border-box;
+  cursor: pointer;
+  font: inherit;
+  -webkit-text-decoration: none;
+  text-decoration: none;
+  margin: 0;
+  background: transparent;
+  overflow: visible;
+  text-transform: none;
+  color: inherit;
+  outline: none;
+  border: none;
+  padding: 0;
+  text-align: inherit;
+  height: 100%;
+}
+
+.c6:focus {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c6:focus > circle,
+.c6:focus > ellipse,
+.c6:focus > line,
+.c6:focus > path,
+.c6:focus > polygon,
+.c6:focus > polyline,
+.c6:focus > rect {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c6:focus::-moz-focus-inner {
+  border: 0;
+}
+
+.c6:focus:not(:focus-visible) {
+  outline: none;
+  box-shadow: none;
+}
+
+.c6:focus:not(:focus-visible) > circle,
+.c6:focus:not(:focus-visible) > ellipse,
+.c6:focus:not(:focus-visible) > line,
+.c6:focus:not(:focus-visible) > path,
+.c6:focus:not(:focus-visible) > polygon,
+.c6:focus:not(:focus-visible) > polyline,
+.c6:focus:not(:focus-visible) > rect {
+  outline: none;
+  box-shadow: none;
+}
+
+.c6:focus:not(:focus-visible)::-moz-focus-inner {
+  border: 0;
+}
+
+.c4 {
+  margin: 0;
+  padding: 0;
+  font-weight: inherit;
+  text-align: inherit;
+  text-align: start;
+  border-bottom: solid 1px rgba(0,0,0,0.33);
+  padding: 0px;
+}
+
+.c13 {
+  margin: 0;
+  padding: 0;
+  font-weight: inherit;
+  text-align: inherit;
+  text-align: start;
+  padding-left: 12px;
+  padding-right: 12px;
+  padding-top: 6px;
+  padding-bottom: 6px;
+}
+
+.c2 {
+  border-spacing: 0;
+  border-collapse: collapse;
+  width: inherit;
+}
+
+.c3 {
+  position: relative;
+  border-spacing: 0;
+  border-collapse: separate;
+  height: 100%;
+}
+
+.c12:focus {
+  outline: 2px solid #6FFFB0;
+}
+
+.c12:focus:not(:focus-visible) {
+  outline: none;
+}
+
+.c7 {
+  padding: 6px 12px;
+}
+
+@media only screen and (max-width:768px) {
+  .c10 {
+    width: 3px;
+  }
+}
+
+<div
+    class="c0"
+  >
+    <button
+      class="c1"
+      type="button"
+    >
+      Sort data
+    </button>
+    <table
+      class="c2 c3"
+    >
+      <thead
+        class="StyledTable__StyledTableHeader-sc-1m3u5g-4 StyledDataTable__StyledDataTableHeader-sc-xrlyjm-4"
+      >
+        <tr
+          class="StyledTable__StyledTableRow-sc-1m3u5g-2 "
+        >
+          <th
+            class="c4 "
+            scope="col"
+          >
+            <div
+              class="c5"
+            >
+              <button
+                class="c6 c7"
+                type="button"
+              >
+                <div
+                  class="c8"
+                >
+                  <span
+                    class="c9"
+                  >
+                    A
+                  </span>
+                  <div
+                    class="c10"
+                  />
+                  <svg
+                    aria-label="FormDown"
+                    class="c11"
+                    viewBox="0 0 24 24"
+                  >
+                    <path
+                      d="m18 9-6 6-6-6"
+                      fill="none"
+                      stroke="#000"
+                      stroke-width="2"
+                    />
+                  </svg>
+                </div>
+              </button>
+            </div>
+          </th>
+          <th
+            class="c4 "
+            scope="col"
+          >
+            <div
+              class="c5"
+            >
+              <button
+                class="c6 c7"
+                type="button"
+              >
+                <div
+                  class="c8"
+                >
+                  <span
+                    class="c9"
+                  >
+                    B
+                  </span>
+                </div>
+              </button>
+            </div>
+          </th>
+        </tr>
+      </thead>
+      <tbody
+        class="StyledTable__StyledTableBody-sc-1m3u5g-3 c12"
+      >
+        <tr
+          class="StyledTable__StyledTableRow-sc-1m3u5g-2 "
+        >
+          <th
+            class="c13 "
+            scope="row"
+          >
+            <div
+              class="c14"
+            >
+              <span
+                class="c15"
+              >
+                zero
+              </span>
+            </div>
+          </th>
+          <td
+            class="c13 "
+          >
+            <div
+              class="c14"
+            >
+              <span
+                class="c9"
+              >
+                0
+              </span>
+            </div>
+          </td>
+        </tr>
+        <tr
+          class="StyledTable__StyledTableRow-sc-1m3u5g-2 "
+        >
+          <th
+            class="c13 "
+            scope="row"
+          >
+            <div
+              class="c14"
+            >
+              <span
+                class="c15"
+              >
+                two
+              </span>
+            </div>
+          </th>
+          <td
+            class="c13 "
+          >
+            <div
+              class="c14"
+            >
+              <span
+                class="c9"
+              >
+                2
+              </span>
+            </div>
+          </td>
+        </tr>
+        <tr
+          class="StyledTable__StyledTableRow-sc-1m3u5g-2 "
+        >
+          <th
+            class="c13 "
+            scope="row"
+          >
+            <div
+              class="c14"
+            >
+              <span
+                class="c15"
+              >
+                one
+              </span>
+            </div>
+          </th>
+          <td
+            class="c13 "
+          >
+            <div
+              class="c14"
+            >
+              <span
+                class="c9"
+              >
+                1
+              </span>
+            </div>
+          </td>
+        </tr>
+      </tbody>
+    </table>
+  </div>
+</DocumentFragment>
+`;
+
 exports[`DataTable sort external 1`] = `
 .c10 {
   display: inline-block;
