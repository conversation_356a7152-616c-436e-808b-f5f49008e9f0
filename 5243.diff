diff --git a/package.json b/package.json
index 839750c946e..ca3c4bfa841 100644
--- a/package.json
+++ b/package.json
@@ -205,7 +205,7 @@
   "bundlesize": [
     {
       "path": "./dist/grommet.min.js",
-      "maxSize": "161 kB"
+      "maxSize": "162 kB"
     }
   ],
   "keywords": [
diff --git a/src/js/components/RadioButton/RadioButton.js b/src/js/components/RadioButton/RadioButton.js
index 2ea305a8bd6..0e80cc9fcf1 100644
--- a/src/js/components/RadioButton/RadioButton.js
+++ b/src/js/components/RadioButton/RadioButton.js
@@ -43,8 +43,20 @@ const RadioButton = forwardRef(
 
     const Icon = theme.radioButton.icons.circle;
     let borderColor = normalizeColor(theme.radioButton.border.color, theme);
+    let backgroundColor = normalizeColor(
+      theme.radioButton.background?.color,
+      theme,
+    );
+
     if (checked) {
       borderColor = normalizeColor(theme.radioButton.color || 'control', theme);
+
+      if (theme.radioButton.check?.background?.color) {
+        backgroundColor = normalizeColor(
+          theme.radioButton.check.background.color,
+          theme,
+        );
+      }
     }
 
     return (
@@ -96,6 +108,7 @@ const RadioButton = forwardRef(
                 size: theme.radioButton.border.width,
                 color: borderColor,
               }}
+              backgroundColor={backgroundColor}
               round={theme.radioButton.check.radius}
             >
               {checked &&
diff --git a/src/js/components/RadioButton/StyledRadioButton.js b/src/js/components/RadioButton/StyledRadioButton.js
index 0b3840cda9e..ab33d0e6d80 100644
--- a/src/js/components/RadioButton/StyledRadioButton.js
+++ b/src/js/components/RadioButton/StyledRadioButton.js
@@ -101,11 +101,7 @@ StyledRadioButtonIcon.defaultProps = {};
 Object.setPrototypeOf(StyledRadioButtonIcon.defaultProps, defaultProps);
 
 const StyledRadioButtonBox = styled.div`
-  background-color: ${(props) =>
-    normalizeColor(
-      props.theme.radioButton.check.background?.color,
-      props.theme,
-    )};
+  background-color: ${(props) => props.backgroundColor};
   ${(props) => props.focus && focusStyle()};
   ${(props) => props.theme.radioButton.check.extend};
 `;
diff --git a/src/js/components/RadioButton/__tests__/RadioButton-test.tsx b/src/js/components/RadioButton/__tests__/RadioButton-test.tsx
index e8865e19a32..d91645ae2f7 100644
--- a/src/js/components/RadioButton/__tests__/RadioButton-test.tsx
+++ b/src/js/components/RadioButton/__tests__/RadioButton-test.tsx
@@ -101,7 +101,7 @@ describe('RadioButton', () => {
     expect(container.firstChild).toMatchSnapshot();
   });
 
-  test('background-color themed', () => {
+  test('background-color check themed', () => {
     const customTheme = {
       radioButton: {
         check: {
@@ -121,6 +121,24 @@ describe('RadioButton', () => {
     expect(container.firstChild).toMatchSnapshot();
   });
 
+  test('background-color themed', () => {
+    const customTheme = {
+      radioButton: {
+        background: {
+          color: 'blue',
+        },
+      },
+    };
+
+    const { container } = render(
+      <Grommet theme={customTheme}>
+        <RadioButton name="test" />
+      </Grommet>,
+    );
+
+    expect(container.firstChild).toMatchSnapshot();
+  });
+
   test('background-color themed symbolic', () => {
     const customTheme = {
       radioButton: {
diff --git a/src/js/components/RadioButton/__tests__/__snapshots__/RadioButton-test.tsx.snap b/src/js/components/RadioButton/__tests__/__snapshots__/RadioButton-test.tsx.snap
index 170e8c902e1..7055aabb543 100644
--- a/src/js/components/RadioButton/__tests__/__snapshots__/RadioButton-test.tsx.snap
+++ b/src/js/components/RadioButton/__tests__/__snapshots__/RadioButton-test.tsx.snap
@@ -1,5 +1,123 @@
 // Jest Snapshot v1, https://goo.gl/fbAQLP
 
+exports[`RadioButton background-color check themed 1`] = `
+.c0 {
+  font-size: 18px;
+  line-height: 24px;
+  box-sizing: border-box;
+  -webkit-text-size-adjust: 100%;
+  -ms-text-size-adjust: 100%;
+  -moz-osx-font-smoothing: grayscale;
+  -webkit-font-smoothing: antialiased;
+}
+
+.c2 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: column;
+  -ms-flex-direction: column;
+  flex-direction: column;
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+}
+
+.c4 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  -webkit-align-items: center;
+  -webkit-box-align: center;
+  -ms-flex-align: center;
+  align-items: center;
+  border: solid 2px rgba(0,0,0,0.15);
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: column;
+  -ms-flex-direction: column;
+  flex-direction: column;
+  height: 24px;
+  width: 24px;
+  -webkit-box-pack: center;
+  -webkit-justify-content: center;
+  -ms-flex-pack: center;
+  justify-content: center;
+  border-radius: 100%;
+}
+
+.c1 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  -webkit-flex-direction: row;
+  -ms-flex-direction: row;
+  flex-direction: row;
+  -webkit-align-items: center;
+  -webkit-box-align: center;
+  -ms-flex-align: center;
+  align-items: center;
+  -webkit-user-select: none;
+  -moz-user-select: none;
+  -ms-user-select: none;
+  user-select: none;
+  width: -webkit-fit-content;
+  width: -moz-fit-content;
+  width: fit-content;
+  cursor: pointer;
+}
+
+.c1:hover input:not([disabled]) + div,
+.c1:hover input:not([disabled]) + span {
+  border-color: #000000;
+}
+
+.c3 {
+  opacity: 0;
+  -moz-appearance: none;
+  width: 0;
+  height: 0;
+  margin: 0;
+  cursor: pointer;
+}
+
+@media only screen and (max-width:768px) {
+  .c4 {
+    border: solid 2px rgba(0,0,0,0.15);
+  }
+}
+
+<div
+  class="c0"
+>
+  <label
+    class="c1"
+  >
+    <div
+      class="c2 "
+    >
+      <input
+        class="c3"
+        name="test"
+        type="radio"
+      />
+      <div
+        class="c4 "
+      />
+    </div>
+  </label>
+</div>
+`;
+
 exports[`RadioButton background-color themed 1`] = `
 .c0 {
   font-size: 18px;
@@ -91,7 +209,7 @@ exports[`RadioButton background-color themed 1`] = `
 }
 
 .c5 {
-  background-color: red;
+  background-color: blue;
 }
 
 @media only screen and (max-width:768px) {
@@ -212,10 +330,6 @@ exports[`RadioButton background-color themed symbolic 1`] = `
   cursor: pointer;
 }
 
-.c5 {
-  background-color: #7D4CDB;
-}
-
 @media only screen and (max-width:768px) {
   .c4 {
     border: solid 2px rgba(0,0,0,0.15);
@@ -237,7 +351,7 @@ exports[`RadioButton background-color themed symbolic 1`] = `
         type="radio"
       />
       <div
-        class="c4 c5"
+        class="c4 "
       />
     </div>
   </label>
diff --git a/src/js/themes/base.d.ts b/src/js/themes/base.d.ts
index 756126e9cd3..cd84d12aa37 100644
--- a/src/js/themes/base.d.ts
+++ b/src/js/themes/base.d.ts
@@ -1315,6 +1315,9 @@ export interface ThemeType {
     container?: {
       extend?: ExtendType;
     };
+    background?: {
+      color?: ColorType;
+    };
     border?: {
       color?: ColorType;
       width?: string;
diff --git a/src/js/themes/base.js b/src/js/themes/base.js
index b30b469a1a0..755f8c4b6da 100644
--- a/src/js/themes/base.js
+++ b/src/js/themes/base.js
@@ -1581,6 +1581,9 @@ export const generate = (baseSpacing = 24, scale = 6) => {
         },
         width: '2px',
       },
+      // background: {
+      //   color: undefined,
+      // },
       check: {
         radius: '100%',
         // background: {
