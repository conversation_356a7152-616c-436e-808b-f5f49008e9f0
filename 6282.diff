diff --git a/src/js/components/DataTable/__tests__/DataTable-test.js b/src/js/components/DataTable/__tests__/DataTable-test.js
index 7103fd482dd..65c0d6fec46 100644
--- a/src/js/components/DataTable/__tests__/DataTable-test.js
+++ b/src/js/components/DataTable/__tests__/DataTable-test.js
@@ -157,6 +157,39 @@ describe('DataTable', () => {
     expect(container.firstChild).toMatchSnapshot();
   });
 
+  test('sort null data', () => {
+    const { container, getByText } = render(
+      <Grommet>
+        <DataTable
+          columns={[
+            { property: 'a', header: 'A' },
+            { property: 'b', header: 'B' },
+            { property: 'c', header: 'C' },
+            { property: 'd', header: 'D' },
+          ]}
+          data={[
+            { a: undefined, b: 0, c: 'first', d: 'y' },
+            { a: 'one', b: 1, c: null },
+            { a: 'two', b: 2, c: 'second' },
+            { a: undefined, b: 3, c: null, d: 'z' },
+          ]}
+          sortable
+        />
+      </Grommet>,
+    );
+    expect(container.firstChild).toMatchSnapshot();
+
+    let headerCell = getByText('A');
+    fireEvent.click(headerCell, {});
+    expect(container.firstChild).toMatchSnapshot();
+    headerCell = getByText('C');
+    fireEvent.click(headerCell, {});
+    expect(container.firstChild).toMatchSnapshot();
+    headerCell = getByText('D');
+    fireEvent.click(headerCell, {});
+    expect(container.firstChild).toMatchSnapshot();
+  });
+
   test('onSort', () => {
     const onSort = jest.fn();
     const { container, getByText } = render(
diff --git a/src/js/components/DataTable/__tests__/__snapshots__/DataTable-test.js.snap b/src/js/components/DataTable/__tests__/__snapshots__/DataTable-test.js.snap
index fa1113a0337..f08c9409f03 100644
--- a/src/js/components/DataTable/__tests__/__snapshots__/DataTable-test.js.snap
+++ b/src/js/components/DataTable/__tests__/__snapshots__/DataTable-test.js.snap
@@ -37680,6 +37680,1701 @@ exports[`DataTable sort nested object with onSort 1`] = `
 </div>
 `;
 
+exports[`DataTable sort null data 1`] = `
+.c0 {
+  font-size: 18px;
+  line-height: 24px;
+  box-sizing: border-box;
+  -webkit-text-size-adjust: 100%;
+  -ms-text-size-adjust: 100%;
+  -moz-osx-font-smoothing: grayscale;
+  -webkit-font-smoothing: antialiased;
+}
+
+.c4 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: column;
+  -ms-flex-direction: column;
+  flex-direction: column;
+  -webkit-flex: 1 0 auto;
+  -ms-flex: 1 0 auto;
+  flex: 1 0 auto;
+}
+
+.c7 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  -webkit-align-items: center;
+  -webkit-box-align: center;
+  -ms-flex-align: center;
+  align-items: center;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: row;
+  -ms-flex-direction: row;
+  flex-direction: row;
+}
+
+.c11 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: column;
+  -ms-flex-direction: column;
+  flex-direction: column;
+}
+
+.c8 {
+  font-size: 18px;
+  line-height: 24px;
+}
+
+.c12 {
+  font-size: 18px;
+  line-height: 24px;
+  font-weight: bold;
+}
+
+.c5 {
+  display: inline-block;
+  box-sizing: border-box;
+  cursor: pointer;
+  font: inherit;
+  -webkit-text-decoration: none;
+  text-decoration: none;
+  margin: 0;
+  background: transparent;
+  overflow: visible;
+  text-transform: none;
+  color: inherit;
+  outline: none;
+  border: none;
+  padding: 0;
+  text-align: inherit;
+  height: 100%;
+}
+
+.c5:focus {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c5:focus > circle,
+.c5:focus > ellipse,
+.c5:focus > line,
+.c5:focus > path,
+.c5:focus > polygon,
+.c5:focus > polyline,
+.c5:focus > rect {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c5:focus::-moz-focus-inner {
+  border: 0;
+}
+
+.c5:focus:not(:focus-visible) {
+  outline: none;
+  box-shadow: none;
+}
+
+.c5:focus:not(:focus-visible) > circle,
+.c5:focus:not(:focus-visible) > ellipse,
+.c5:focus:not(:focus-visible) > line,
+.c5:focus:not(:focus-visible) > path,
+.c5:focus:not(:focus-visible) > polygon,
+.c5:focus:not(:focus-visible) > polyline,
+.c5:focus:not(:focus-visible) > rect {
+  outline: none;
+  box-shadow: none;
+}
+
+.c5:focus:not(:focus-visible)::-moz-focus-inner {
+  border: 0;
+}
+
+.c3 {
+  margin: 0;
+  padding: 0;
+  font-weight: inherit;
+  text-align: inherit;
+  text-align: start;
+  border-bottom: solid 1px rgba(0,0,0,0.33);
+  padding: 0px;
+}
+
+.c10 {
+  margin: 0;
+  padding: 0;
+  font-weight: inherit;
+  text-align: inherit;
+  text-align: start;
+  padding-left: 12px;
+  padding-right: 12px;
+  padding-top: 6px;
+  padding-bottom: 6px;
+}
+
+.c1 {
+  border-spacing: 0;
+  border-collapse: collapse;
+  width: inherit;
+}
+
+.c2 {
+  position: relative;
+  border-spacing: 0;
+  border-collapse: separate;
+  height: 100%;
+}
+
+.c9:focus {
+  outline: 2px solid #6FFFB0;
+}
+
+.c9:focus:not(:focus-visible) {
+  outline: none;
+}
+
+.c6 {
+  padding: 6px 12px;
+}
+
+<div
+  class="c0"
+>
+  <table
+    class="c1 c2"
+  >
+    <thead
+      class="StyledTable__StyledTableHeader-sc-1m3u5g-4 StyledDataTable__StyledDataTableHeader-sc-xrlyjm-4"
+    >
+      <tr
+        class="StyledTable__StyledTableRow-sc-1m3u5g-2 "
+      >
+        <th
+          class="c3 "
+          scope="col"
+        >
+          <div
+            class="c4"
+          >
+            <button
+              class="c5 c6"
+              type="button"
+            >
+              <div
+                class="c7"
+              >
+                <span
+                  class="c8"
+                >
+                  A
+                </span>
+              </div>
+            </button>
+          </div>
+        </th>
+        <th
+          class="c3 "
+          scope="col"
+        >
+          <div
+            class="c4"
+          >
+            <button
+              class="c5 c6"
+              type="button"
+            >
+              <div
+                class="c7"
+              >
+                <span
+                  class="c8"
+                >
+                  B
+                </span>
+              </div>
+            </button>
+          </div>
+        </th>
+        <th
+          class="c3 "
+          scope="col"
+        >
+          <div
+            class="c4"
+          >
+            <button
+              class="c5 c6"
+              type="button"
+            >
+              <div
+                class="c7"
+              >
+                <span
+                  class="c8"
+                >
+                  C
+                </span>
+              </div>
+            </button>
+          </div>
+        </th>
+        <th
+          class="c3 "
+          scope="col"
+        >
+          <div
+            class="c4"
+          >
+            <button
+              class="c5 c6"
+              type="button"
+            >
+              <div
+                class="c7"
+              >
+                <span
+                  class="c8"
+                >
+                  D
+                </span>
+              </div>
+            </button>
+          </div>
+        </th>
+      </tr>
+    </thead>
+    <tbody
+      class="StyledTable__StyledTableBody-sc-1m3u5g-3 c9"
+    >
+      <tr
+        class="StyledTable__StyledTableRow-sc-1m3u5g-2 "
+      >
+        <th
+          class="c10 "
+          scope="row"
+        >
+          <div
+            class="c11"
+          />
+        </th>
+        <td
+          class="c10 "
+        >
+          <div
+            class="c11"
+          >
+            <span
+              class="c8"
+            >
+              0
+            </span>
+          </div>
+        </td>
+        <td
+          class="c10 "
+        >
+          <div
+            class="c11"
+          >
+            <span
+              class="c8"
+            >
+              first
+            </span>
+          </div>
+        </td>
+        <td
+          class="c10 "
+        >
+          <div
+            class="c11"
+          >
+            <span
+              class="c8"
+            >
+              y
+            </span>
+          </div>
+        </td>
+      </tr>
+      <tr
+        class="StyledTable__StyledTableRow-sc-1m3u5g-2 "
+      >
+        <th
+          class="c10 "
+          scope="row"
+        >
+          <div
+            class="c11"
+          >
+            <span
+              class="c12"
+            >
+              one
+            </span>
+          </div>
+        </th>
+        <td
+          class="c10 "
+        >
+          <div
+            class="c11"
+          >
+            <span
+              class="c8"
+            >
+              1
+            </span>
+          </div>
+        </td>
+        <td
+          class="c10 "
+        >
+          <div
+            class="c11"
+          />
+        </td>
+        <td
+          class="c10 "
+        >
+          <div
+            class="c11"
+          />
+        </td>
+      </tr>
+      <tr
+        class="StyledTable__StyledTableRow-sc-1m3u5g-2 "
+      >
+        <th
+          class="c10 "
+          scope="row"
+        >
+          <div
+            class="c11"
+          >
+            <span
+              class="c12"
+            >
+              two
+            </span>
+          </div>
+        </th>
+        <td
+          class="c10 "
+        >
+          <div
+            class="c11"
+          >
+            <span
+              class="c8"
+            >
+              2
+            </span>
+          </div>
+        </td>
+        <td
+          class="c10 "
+        >
+          <div
+            class="c11"
+          >
+            <span
+              class="c8"
+            >
+              second
+            </span>
+          </div>
+        </td>
+        <td
+          class="c10 "
+        >
+          <div
+            class="c11"
+          />
+        </td>
+      </tr>
+      <tr
+        class="StyledTable__StyledTableRow-sc-1m3u5g-2 "
+      >
+        <th
+          class="c10 "
+          scope="row"
+        >
+          <div
+            class="c11"
+          />
+        </th>
+        <td
+          class="c10 "
+        >
+          <div
+            class="c11"
+          >
+            <span
+              class="c8"
+            >
+              3
+            </span>
+          </div>
+        </td>
+        <td
+          class="c10 "
+        >
+          <div
+            class="c11"
+          />
+        </td>
+        <td
+          class="c10 "
+        >
+          <div
+            class="c11"
+          >
+            <span
+              class="c8"
+            >
+              z
+            </span>
+          </div>
+        </td>
+      </tr>
+    </tbody>
+  </table>
+</div>
+`;
+
+exports[`DataTable sort null data 2`] = `
+<div
+  class="StyledGrommet-sc-19lkkz7-0 bFgzgw"
+>
+  <table
+    class="StyledTable-sc-1m3u5g-6 YPHTu StyledDataTable-sc-xrlyjm-0 hmFzTO"
+  >
+    <thead
+      class="StyledTable__StyledTableHeader-sc-1m3u5g-4 StyledDataTable__StyledDataTableHeader-sc-xrlyjm-4"
+    >
+      <tr
+        class="StyledTable__StyledTableRow-sc-1m3u5g-2 StyledDataTable__StyledDataTableRow-sc-xrlyjm-2 hSlrxd"
+      >
+        <th
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 ILkGj StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+          scope="col"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 fqUnkf"
+          >
+            <button
+              class="StyledButton-sc-323bzc-0 iIPNKk Header__StyledHeaderCellButton-sc-1baku5q-0 jdyvfX"
+              type="button"
+            >
+              <div
+                class="StyledBox-sc-13pk1d4-0 cwefyf"
+              >
+                <span
+                  class="StyledText-sc-1sadyjn-0 hpCRgD"
+                >
+                  A
+                </span>
+                .c0 {
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+  -webkit-align-self: stretch;
+  -ms-flex-item-align: stretch;
+  align-self: stretch;
+  width: 6px;
+}
+
+@media only screen and (max-width:768px) {
+  .c0 {
+    width: 3px;
+  }
+}
+
+<div
+                  class="c0"
+                />
+                .c0 {
+  display: inline-block;
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+  width: 24px;
+  height: 24px;
+  fill: #666666;
+  stroke: #666666;
+}
+
+.c0 g {
+  fill: inherit;
+  stroke: inherit;
+}
+
+.c0 *:not([stroke])[fill="none"] {
+  stroke-width: 0;
+}
+
+.c0 *[stroke*="#"],
+.c0 *[STROKE*="#"] {
+  stroke: inherit;
+  fill: none;
+}
+
+.c0 *[fill-rule],
+.c0 *[FILL-RULE],
+.c0 *[fill*="#"],
+.c0 *[FILL*="#"] {
+  fill: inherit;
+  stroke: none;
+}
+
+@media only screen and (max-width:768px) {
+
+}
+
+<svg
+                  aria-label="FormUp"
+                  class="c0"
+                  viewBox="0 0 24 24"
+                >
+                  <path
+                    d="m18 15-6-6-6 6"
+                    fill="none"
+                    stroke="#000"
+                    stroke-width="2"
+                  />
+                </svg>
+              </div>
+            </button>
+          </div>
+        </th>
+        <th
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 ILkGj StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+          scope="col"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 fqUnkf"
+          >
+            <button
+              class="StyledButton-sc-323bzc-0 iIPNKk Header__StyledHeaderCellButton-sc-1baku5q-0 jdyvfX"
+              type="button"
+            >
+              <div
+                class="StyledBox-sc-13pk1d4-0 cwefyf"
+              >
+                <span
+                  class="StyledText-sc-1sadyjn-0 hpCRgD"
+                >
+                  B
+                </span>
+              </div>
+            </button>
+          </div>
+        </th>
+        <th
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 ILkGj StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+          scope="col"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 fqUnkf"
+          >
+            <button
+              class="StyledButton-sc-323bzc-0 iIPNKk Header__StyledHeaderCellButton-sc-1baku5q-0 jdyvfX"
+              type="button"
+            >
+              <div
+                class="StyledBox-sc-13pk1d4-0 cwefyf"
+              >
+                <span
+                  class="StyledText-sc-1sadyjn-0 hpCRgD"
+                >
+                  C
+                </span>
+              </div>
+            </button>
+          </div>
+        </th>
+        <th
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 ILkGj StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+          scope="col"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 fqUnkf"
+          >
+            <button
+              class="StyledButton-sc-323bzc-0 iIPNKk Header__StyledHeaderCellButton-sc-1baku5q-0 jdyvfX"
+              type="button"
+            >
+              <div
+                class="StyledBox-sc-13pk1d4-0 cwefyf"
+              >
+                <span
+                  class="StyledText-sc-1sadyjn-0 hpCRgD"
+                >
+                  D
+                </span>
+              </div>
+            </button>
+          </div>
+        </th>
+      </tr>
+    </thead>
+    <tbody
+      class="StyledTable__StyledTableBody-sc-1m3u5g-3 StyledDataTable__StyledDataTableBody-sc-xrlyjm-3 eaVduh"
+    >
+      <tr
+        class="StyledTable__StyledTableRow-sc-1m3u5g-2 StyledDataTable__StyledDataTableRow-sc-xrlyjm-2 hSlrxd"
+      >
+        <th
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+          scope="row"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          />
+        </th>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          >
+            <span
+              class="StyledText-sc-1sadyjn-0 hpCRgD"
+            >
+              0
+            </span>
+          </div>
+        </td>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          >
+            <span
+              class="StyledText-sc-1sadyjn-0 hpCRgD"
+            >
+              first
+            </span>
+          </div>
+        </td>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          >
+            <span
+              class="StyledText-sc-1sadyjn-0 hpCRgD"
+            >
+              y
+            </span>
+          </div>
+        </td>
+      </tr>
+      <tr
+        class="StyledTable__StyledTableRow-sc-1m3u5g-2 StyledDataTable__StyledDataTableRow-sc-xrlyjm-2 hSlrxd"
+      >
+        <th
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+          scope="row"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          />
+        </th>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          >
+            <span
+              class="StyledText-sc-1sadyjn-0 hpCRgD"
+            >
+              3
+            </span>
+          </div>
+        </td>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          />
+        </td>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          >
+            .c0 {
+  font-size: 18px;
+  line-height: 24px;
+}
+
+@media only screen and (max-width:768px) {
+
+}
+
+<span
+              class="c0"
+            >
+              z
+            </span>
+          </div>
+        </td>
+      </tr>
+      <tr
+        class="StyledTable__StyledTableRow-sc-1m3u5g-2 StyledDataTable__StyledDataTableRow-sc-xrlyjm-2 hSlrxd"
+      >
+        <th
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+          scope="row"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          >
+            <span
+              class="StyledText-sc-1sadyjn-0 cdHUSp"
+            >
+              one
+            </span>
+          </div>
+        </th>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          >
+            <span
+              class="StyledText-sc-1sadyjn-0 hpCRgD"
+            >
+              1
+            </span>
+          </div>
+        </td>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          />
+        </td>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          />
+        </td>
+      </tr>
+      <tr
+        class="StyledTable__StyledTableRow-sc-1m3u5g-2 StyledDataTable__StyledDataTableRow-sc-xrlyjm-2 hSlrxd"
+      >
+        <th
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+          scope="row"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          >
+            .c0 {
+  font-size: 18px;
+  line-height: 24px;
+  font-weight: bold;
+}
+
+@media only screen and (max-width:768px) {
+
+}
+
+<span
+              class="c0"
+            >
+              two
+            </span>
+          </div>
+        </th>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          >
+            <span
+              class="StyledText-sc-1sadyjn-0 hpCRgD"
+            >
+              2
+            </span>
+          </div>
+        </td>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          >
+            .c0 {
+  font-size: 18px;
+  line-height: 24px;
+}
+
+@media only screen and (max-width:768px) {
+
+}
+
+<span
+              class="c0"
+            >
+              second
+            </span>
+          </div>
+        </td>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          />
+        </td>
+      </tr>
+    </tbody>
+  </table>
+</div>
+`;
+
+exports[`DataTable sort null data 3`] = `
+<div
+  class="StyledGrommet-sc-19lkkz7-0 bFgzgw"
+>
+  <table
+    class="StyledTable-sc-1m3u5g-6 YPHTu StyledDataTable-sc-xrlyjm-0 hmFzTO"
+  >
+    <thead
+      class="StyledTable__StyledTableHeader-sc-1m3u5g-4 StyledDataTable__StyledDataTableHeader-sc-xrlyjm-4"
+    >
+      <tr
+        class="StyledTable__StyledTableRow-sc-1m3u5g-2 StyledDataTable__StyledDataTableRow-sc-xrlyjm-2 hSlrxd"
+      >
+        <th
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 ILkGj StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+          scope="col"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 fqUnkf"
+          >
+            <button
+              class="StyledButton-sc-323bzc-0 iIPNKk Header__StyledHeaderCellButton-sc-1baku5q-0 jdyvfX"
+              type="button"
+            >
+              <div
+                class="StyledBox-sc-13pk1d4-0 cwefyf"
+              >
+                <span
+                  class="StyledText-sc-1sadyjn-0 hpCRgD"
+                >
+                  A
+                </span>
+              </div>
+            </button>
+          </div>
+        </th>
+        <th
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 ILkGj StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+          scope="col"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 fqUnkf"
+          >
+            <button
+              class="StyledButton-sc-323bzc-0 iIPNKk Header__StyledHeaderCellButton-sc-1baku5q-0 jdyvfX"
+              type="button"
+            >
+              <div
+                class="StyledBox-sc-13pk1d4-0 cwefyf"
+              >
+                <span
+                  class="StyledText-sc-1sadyjn-0 hpCRgD"
+                >
+                  B
+                </span>
+              </div>
+            </button>
+          </div>
+        </th>
+        <th
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 ILkGj StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+          scope="col"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 fqUnkf"
+          >
+            <button
+              class="StyledButton-sc-323bzc-0 iIPNKk Header__StyledHeaderCellButton-sc-1baku5q-0 jdyvfX"
+              type="button"
+            >
+              <div
+                class="StyledBox-sc-13pk1d4-0 cwefyf"
+              >
+                <span
+                  class="StyledText-sc-1sadyjn-0 hpCRgD"
+                >
+                  C
+                </span>
+                .c0 {
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+  -webkit-align-self: stretch;
+  -ms-flex-item-align: stretch;
+  align-self: stretch;
+  width: 6px;
+}
+
+@media only screen and (max-width:768px) {
+  .c0 {
+    width: 3px;
+  }
+}
+
+<div
+                  class="c0"
+                />
+                .c0 {
+  display: inline-block;
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+  width: 24px;
+  height: 24px;
+  fill: #666666;
+  stroke: #666666;
+}
+
+.c0 g {
+  fill: inherit;
+  stroke: inherit;
+}
+
+.c0 *:not([stroke])[fill="none"] {
+  stroke-width: 0;
+}
+
+.c0 *[stroke*="#"],
+.c0 *[STROKE*="#"] {
+  stroke: inherit;
+  fill: none;
+}
+
+.c0 *[fill-rule],
+.c0 *[FILL-RULE],
+.c0 *[fill*="#"],
+.c0 *[FILL*="#"] {
+  fill: inherit;
+  stroke: none;
+}
+
+@media only screen and (max-width:768px) {
+
+}
+
+<svg
+                  aria-label="FormUp"
+                  class="c0"
+                  viewBox="0 0 24 24"
+                >
+                  <path
+                    d="m18 15-6-6-6 6"
+                    fill="none"
+                    stroke="#000"
+                    stroke-width="2"
+                  />
+                </svg>
+              </div>
+            </button>
+          </div>
+        </th>
+        <th
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 ILkGj StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+          scope="col"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 fqUnkf"
+          >
+            <button
+              class="StyledButton-sc-323bzc-0 iIPNKk Header__StyledHeaderCellButton-sc-1baku5q-0 jdyvfX"
+              type="button"
+            >
+              <div
+                class="StyledBox-sc-13pk1d4-0 cwefyf"
+              >
+                <span
+                  class="StyledText-sc-1sadyjn-0 hpCRgD"
+                >
+                  D
+                </span>
+              </div>
+            </button>
+          </div>
+        </th>
+      </tr>
+    </thead>
+    <tbody
+      class="StyledTable__StyledTableBody-sc-1m3u5g-3 StyledDataTable__StyledDataTableBody-sc-xrlyjm-3 eaVduh"
+    >
+      <tr
+        class="StyledTable__StyledTableRow-sc-1m3u5g-2 StyledDataTable__StyledDataTableRow-sc-xrlyjm-2 hSlrxd"
+      >
+        <th
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+          scope="row"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          >
+            .c0 {
+  font-size: 18px;
+  line-height: 24px;
+  font-weight: bold;
+}
+
+@media only screen and (max-width:768px) {
+
+}
+
+<span
+              class="c0"
+            >
+              one
+            </span>
+          </div>
+        </th>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          >
+            <span
+              class="StyledText-sc-1sadyjn-0 hpCRgD"
+            >
+              1
+            </span>
+          </div>
+        </td>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          />
+        </td>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          />
+        </td>
+      </tr>
+      <tr
+        class="StyledTable__StyledTableRow-sc-1m3u5g-2 StyledDataTable__StyledDataTableRow-sc-xrlyjm-2 hSlrxd"
+      >
+        <th
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+          scope="row"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          />
+        </th>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          >
+            <span
+              class="StyledText-sc-1sadyjn-0 hpCRgD"
+            >
+              3
+            </span>
+          </div>
+        </td>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          />
+        </td>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          >
+            <span
+              class="StyledText-sc-1sadyjn-0 hpCRgD"
+            >
+              z
+            </span>
+          </div>
+        </td>
+      </tr>
+      <tr
+        class="StyledTable__StyledTableRow-sc-1m3u5g-2 StyledDataTable__StyledDataTableRow-sc-xrlyjm-2 hSlrxd"
+      >
+        <th
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+          scope="row"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          />
+        </th>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          >
+            <span
+              class="StyledText-sc-1sadyjn-0 hpCRgD"
+            >
+              0
+            </span>
+          </div>
+        </td>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          >
+            .c0 {
+  font-size: 18px;
+  line-height: 24px;
+}
+
+@media only screen and (max-width:768px) {
+
+}
+
+<span
+              class="c0"
+            >
+              first
+            </span>
+          </div>
+        </td>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          >
+            .c0 {
+  font-size: 18px;
+  line-height: 24px;
+}
+
+@media only screen and (max-width:768px) {
+
+}
+
+<span
+              class="c0"
+            >
+              y
+            </span>
+          </div>
+        </td>
+      </tr>
+      <tr
+        class="StyledTable__StyledTableRow-sc-1m3u5g-2 StyledDataTable__StyledDataTableRow-sc-xrlyjm-2 hSlrxd"
+      >
+        <th
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+          scope="row"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          >
+            <span
+              class="StyledText-sc-1sadyjn-0 cdHUSp"
+            >
+              two
+            </span>
+          </div>
+        </th>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          >
+            <span
+              class="StyledText-sc-1sadyjn-0 hpCRgD"
+            >
+              2
+            </span>
+          </div>
+        </td>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          >
+            <span
+              class="StyledText-sc-1sadyjn-0 hpCRgD"
+            >
+              second
+            </span>
+          </div>
+        </td>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          />
+        </td>
+      </tr>
+    </tbody>
+  </table>
+</div>
+`;
+
+exports[`DataTable sort null data 4`] = `
+<div
+  class="StyledGrommet-sc-19lkkz7-0 bFgzgw"
+>
+  <table
+    class="StyledTable-sc-1m3u5g-6 YPHTu StyledDataTable-sc-xrlyjm-0 hmFzTO"
+  >
+    <thead
+      class="StyledTable__StyledTableHeader-sc-1m3u5g-4 StyledDataTable__StyledDataTableHeader-sc-xrlyjm-4"
+    >
+      <tr
+        class="StyledTable__StyledTableRow-sc-1m3u5g-2 StyledDataTable__StyledDataTableRow-sc-xrlyjm-2 hSlrxd"
+      >
+        <th
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 ILkGj StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+          scope="col"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 fqUnkf"
+          >
+            <button
+              class="StyledButton-sc-323bzc-0 iIPNKk Header__StyledHeaderCellButton-sc-1baku5q-0 jdyvfX"
+              type="button"
+            >
+              <div
+                class="StyledBox-sc-13pk1d4-0 cwefyf"
+              >
+                <span
+                  class="StyledText-sc-1sadyjn-0 hpCRgD"
+                >
+                  A
+                </span>
+              </div>
+            </button>
+          </div>
+        </th>
+        <th
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 ILkGj StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+          scope="col"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 fqUnkf"
+          >
+            <button
+              class="StyledButton-sc-323bzc-0 iIPNKk Header__StyledHeaderCellButton-sc-1baku5q-0 jdyvfX"
+              type="button"
+            >
+              <div
+                class="StyledBox-sc-13pk1d4-0 cwefyf"
+              >
+                <span
+                  class="StyledText-sc-1sadyjn-0 hpCRgD"
+                >
+                  B
+                </span>
+              </div>
+            </button>
+          </div>
+        </th>
+        <th
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 ILkGj StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+          scope="col"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 fqUnkf"
+          >
+            <button
+              class="StyledButton-sc-323bzc-0 iIPNKk Header__StyledHeaderCellButton-sc-1baku5q-0 jdyvfX"
+              type="button"
+            >
+              <div
+                class="StyledBox-sc-13pk1d4-0 cwefyf"
+              >
+                <span
+                  class="StyledText-sc-1sadyjn-0 hpCRgD"
+                >
+                  C
+                </span>
+              </div>
+            </button>
+          </div>
+        </th>
+        <th
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 ILkGj StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+          scope="col"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 fqUnkf"
+          >
+            <button
+              class="StyledButton-sc-323bzc-0 iIPNKk Header__StyledHeaderCellButton-sc-1baku5q-0 jdyvfX"
+              type="button"
+            >
+              <div
+                class="StyledBox-sc-13pk1d4-0 cwefyf"
+              >
+                <span
+                  class="StyledText-sc-1sadyjn-0 hpCRgD"
+                >
+                  D
+                </span>
+                .c0 {
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+  -webkit-align-self: stretch;
+  -ms-flex-item-align: stretch;
+  align-self: stretch;
+  width: 6px;
+}
+
+@media only screen and (max-width:768px) {
+  .c0 {
+    width: 3px;
+  }
+}
+
+<div
+                  class="c0"
+                />
+                .c0 {
+  display: inline-block;
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+  width: 24px;
+  height: 24px;
+  fill: #666666;
+  stroke: #666666;
+}
+
+.c0 g {
+  fill: inherit;
+  stroke: inherit;
+}
+
+.c0 *:not([stroke])[fill="none"] {
+  stroke-width: 0;
+}
+
+.c0 *[stroke*="#"],
+.c0 *[STROKE*="#"] {
+  stroke: inherit;
+  fill: none;
+}
+
+.c0 *[fill-rule],
+.c0 *[FILL-RULE],
+.c0 *[fill*="#"],
+.c0 *[FILL*="#"] {
+  fill: inherit;
+  stroke: none;
+}
+
+@media only screen and (max-width:768px) {
+
+}
+
+<svg
+                  aria-label="FormUp"
+                  class="c0"
+                  viewBox="0 0 24 24"
+                >
+                  <path
+                    d="m18 15-6-6-6 6"
+                    fill="none"
+                    stroke="#000"
+                    stroke-width="2"
+                  />
+                </svg>
+              </div>
+            </button>
+          </div>
+        </th>
+      </tr>
+    </thead>
+    <tbody
+      class="StyledTable__StyledTableBody-sc-1m3u5g-3 StyledDataTable__StyledDataTableBody-sc-xrlyjm-3 eaVduh"
+    >
+      <tr
+        class="StyledTable__StyledTableRow-sc-1m3u5g-2 StyledDataTable__StyledDataTableRow-sc-xrlyjm-2 hSlrxd"
+      >
+        <th
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+          scope="row"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          >
+            <span
+              class="StyledText-sc-1sadyjn-0 cdHUSp"
+            >
+              one
+            </span>
+          </div>
+        </th>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          >
+            <span
+              class="StyledText-sc-1sadyjn-0 hpCRgD"
+            >
+              1
+            </span>
+          </div>
+        </td>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          />
+        </td>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          />
+        </td>
+      </tr>
+      <tr
+        class="StyledTable__StyledTableRow-sc-1m3u5g-2 StyledDataTable__StyledDataTableRow-sc-xrlyjm-2 hSlrxd"
+      >
+        <th
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+          scope="row"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          >
+            .c0 {
+  font-size: 18px;
+  line-height: 24px;
+  font-weight: bold;
+}
+
+@media only screen and (max-width:768px) {
+
+}
+
+<span
+              class="c0"
+            >
+              two
+            </span>
+          </div>
+        </th>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          >
+            <span
+              class="StyledText-sc-1sadyjn-0 hpCRgD"
+            >
+              2
+            </span>
+          </div>
+        </td>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          >
+            .c0 {
+  font-size: 18px;
+  line-height: 24px;
+}
+
+@media only screen and (max-width:768px) {
+
+}
+
+<span
+              class="c0"
+            >
+              second
+            </span>
+          </div>
+        </td>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          />
+        </td>
+      </tr>
+      <tr
+        class="StyledTable__StyledTableRow-sc-1m3u5g-2 StyledDataTable__StyledDataTableRow-sc-xrlyjm-2 hSlrxd"
+      >
+        <th
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+          scope="row"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          />
+        </th>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          >
+            <span
+              class="StyledText-sc-1sadyjn-0 hpCRgD"
+            >
+              0
+            </span>
+          </div>
+        </td>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          >
+            <span
+              class="StyledText-sc-1sadyjn-0 hpCRgD"
+            >
+              first
+            </span>
+          </div>
+        </td>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          >
+            <span
+              class="StyledText-sc-1sadyjn-0 hpCRgD"
+            >
+              y
+            </span>
+          </div>
+        </td>
+      </tr>
+      <tr
+        class="StyledTable__StyledTableRow-sc-1m3u5g-2 StyledDataTable__StyledDataTableRow-sc-xrlyjm-2 hSlrxd"
+      >
+        <th
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+          scope="row"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          />
+        </th>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          >
+            <span
+              class="StyledText-sc-1sadyjn-0 hpCRgD"
+            >
+              3
+            </span>
+          </div>
+        </td>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          />
+        </td>
+        <td
+          class="StyledTable__StyledTableCell-sc-1m3u5g-0 cuDSKl StyledDataTable__StyledDataTableCell-sc-xrlyjm-6 dsFDzi"
+        >
+          <div
+            class="StyledBox-sc-13pk1d4-0 iNDmPr"
+          >
+            .c0 {
+  font-size: 18px;
+  line-height: 24px;
+}
+
+@media only screen and (max-width:768px) {
+
+}
+
+<span
+              class="c0"
+            >
+              z
+            </span>
+          </div>
+        </td>
+      </tr>
+    </tbody>
+  </table>
+</div>
+`;
+
 exports[`DataTable sortable 1`] = `
 .c0 {
   font-size: 18px;
diff --git a/src/js/components/DataTable/buildState.js b/src/js/components/DataTable/buildState.js
index 619ffda7826..b9b8e11b600 100644
--- a/src/js/components/DataTable/buildState.js
+++ b/src/js/components/DataTable/buildState.js
@@ -90,8 +90,10 @@ export const filterAndSortData = (data, filters, onSearch, sort) => {
     result.sort((d1, d2) => {
       const d1Val = datumValue(d1, property);
       const d2Val = datumValue(d2, property);
-      if (typeof d1Val === 'string' && typeof d2Val === 'string') {
-        const sortResult = d1Val.localeCompare(d2Val, undefined, {
+      if ((typeof d1Val === 'string' && typeof d2Val === 'string') ||
+        (typeof d1Val === 'string' && !d2Val) ||
+        (typeof d2Val === 'string' && !d1Val)) {
+        const sortResult = (d1Val || '').localeCompare(d2Val || '', undefined, {
           sensitivity: 'base',
         });
         return sortAsc ? sortResult : -sortResult;
