diff --git a/src/js/components/FormField/__tests__/__snapshots__/FormField-test.js.snap b/src/js/components/FormField/__tests__/__snapshots__/FormField-test.js.snap
index 0b89c9d2183..3793fba1097 100644
--- a/src/js/components/FormField/__tests__/__snapshots__/FormField-test.js.snap
+++ b/src/js/components/FormField/__tests__/__snapshots__/FormField-test.js.snap
@@ -58,6 +58,7 @@ exports[`renders 1`] = `
   font-weight: 600;
   margin: 0;
   width: 100%;
+  -webkit-appearance: textfield;
   border: none;
   -webkit-appearance: none;
 }
diff --git a/src/js/components/Select/__tests__/__snapshots__/Select-test.js.snap b/src/js/components/Select/__tests__/__snapshots__/Select-test.js.snap
index 9a8d07cfd20..ea45cf68975 100644
--- a/src/js/components/Select/__tests__/__snapshots__/Select-test.js.snap
+++ b/src/js/components/Select/__tests__/__snapshots__/Select-test.js.snap
@@ -126,6 +126,7 @@ exports[`Select basic 1`] = `
   font-weight: 600;
   margin: 0;
   width: 100%;
+  -webkit-appearance: textfield;
   border: none;
   -webkit-appearance: none;
 }
@@ -287,7 +288,7 @@ exports[`Select complex options and children 1`] = `
       >
         <input
           autocomplete="off"
-          class="Select__SelectTextInput-lnXFDk LQZdV StyledTextInput-bzOzsW enFJba"
+          class="Select__SelectTextInput-lnXFDk LQZdV StyledTextInput-bzOzsW laePbP"
           data-testid="test-select"
           id="test-select"
           readonly=""
@@ -342,7 +343,7 @@ exports[`Select complex options and children 2`] = `
       >
         <input
           autocomplete="off"
-          class="Select__SelectTextInput-lnXFDk LQZdV StyledTextInput-bzOzsW enFJba"
+          class="Select__SelectTextInput-lnXFDk LQZdV StyledTextInput-bzOzsW laePbP"
           data-testid="test-select"
           id="test-select"
           readonly=""
@@ -561,7 +562,7 @@ exports[`Select deselect an option 1`] = `
       >
         <input
           autocomplete="off"
-          class="Select__SelectTextInput-lnXFDk LQZdV StyledTextInput-bzOzsW enFJba"
+          class="Select__SelectTextInput-lnXFDk LQZdV StyledTextInput-bzOzsW laePbP"
           data-testid="test-select"
           id="test-select"
           multiple=""
@@ -618,7 +619,7 @@ exports[`Select disabled 1`] = `
       >
         <input
           autocomplete="off"
-          class="Select__SelectTextInput-lnXFDk LQZdV StyledTextInput-bzOzsW enFJba"
+          class="Select__SelectTextInput-lnXFDk LQZdV StyledTextInput-bzOzsW laePbP"
           data-testid="test-select"
           id="test-select"
           readonly=""
@@ -674,7 +675,7 @@ exports[`Select disabled 2`] = `
       >
         <input
           autocomplete="off"
-          class="Select__SelectTextInput-lnXFDk LQZdV StyledTextInput-bzOzsW enFJba"
+          class="Select__SelectTextInput-lnXFDk LQZdV StyledTextInput-bzOzsW laePbP"
           data-testid="test-select"
           id="test-select"
           readonly=""
@@ -836,6 +837,7 @@ exports[`Select multiple 1`] = `
   font-weight: 600;
   margin: 0;
   width: 100%;
+  -webkit-appearance: textfield;
   border: none;
   -webkit-appearance: none;
 }
@@ -1002,7 +1004,7 @@ exports[`Select multiple values 1`] = `
       >
         <input
           autocomplete="off"
-          class="Select__SelectTextInput-lnXFDk LQZdV StyledTextInput-bzOzsW enFJba"
+          class="Select__SelectTextInput-lnXFDk LQZdV StyledTextInput-bzOzsW laePbP"
           data-testid="test-select"
           id="test-select"
           multiple=""
@@ -1058,7 +1060,7 @@ exports[`Select multiple values 2`] = `
       >
         <input
           autocomplete="off"
-          class="Select__SelectTextInput-lnXFDk LQZdV StyledTextInput-bzOzsW enFJba"
+          class="Select__SelectTextInput-lnXFDk LQZdV StyledTextInput-bzOzsW laePbP"
           data-testid="test-select"
           id="test-select"
           multiple=""
@@ -1328,7 +1330,7 @@ exports[`Select opens 1`] = `
       >
         <input
           autocomplete="off"
-          class="Select__SelectTextInput-lnXFDk LQZdV StyledTextInput-bzOzsW enFJba"
+          class="Select__SelectTextInput-lnXFDk LQZdV StyledTextInput-bzOzsW laePbP"
           data-testid="test-select"
           id="test-select"
           readonly=""
@@ -1383,7 +1385,7 @@ exports[`Select opens 2`] = `
       >
         <input
           autocomplete="off"
-          class="Select__SelectTextInput-lnXFDk LQZdV StyledTextInput-bzOzsW enFJba"
+          class="Select__SelectTextInput-lnXFDk LQZdV StyledTextInput-bzOzsW laePbP"
           data-testid="test-select"
           id="test-select"
           readonly=""
@@ -1653,7 +1655,7 @@ exports[`Select search 1`] = `
       >
         <input
           autocomplete="off"
-          class="Select__SelectTextInput-lnXFDk LQZdV StyledTextInput-bzOzsW enFJba"
+          class="Select__SelectTextInput-lnXFDk LQZdV StyledTextInput-bzOzsW laePbP"
           data-testid="test-select"
           id="test-select"
           readonly=""
@@ -1708,7 +1710,7 @@ exports[`Select search 2`] = `
       >
         <input
           autocomplete="off"
-          class="StyledTextInput-bzOzsW hjbdek"
+          class="StyledTextInput-bzOzsW gvXSaz"
           type="search"
           value=""
         />
@@ -1910,7 +1912,7 @@ exports[`Select select an option 1`] = `
       >
         <input
           autocomplete="off"
-          class="Select__SelectTextInput-lnXFDk LQZdV StyledTextInput-bzOzsW enFJba"
+          class="Select__SelectTextInput-lnXFDk LQZdV StyledTextInput-bzOzsW laePbP"
           data-testid="test-select"
           id="test-select"
           readonly=""
@@ -2009,7 +2011,7 @@ exports[`Select select an option with enter 1`] = `
       >
         <input
           autocomplete="off"
-          class="Select__SelectTextInput-lnXFDk LQZdV StyledTextInput-bzOzsW enFJba"
+          class="Select__SelectTextInput-lnXFDk LQZdV StyledTextInput-bzOzsW laePbP"
           data-testid="test-select"
           id="test-select"
           readonly=""
@@ -2064,7 +2066,7 @@ exports[`Select select another option 1`] = `
       >
         <input
           autocomplete="off"
-          class="Select__SelectTextInput-lnXFDk LQZdV StyledTextInput-bzOzsW enFJba"
+          class="Select__SelectTextInput-lnXFDk LQZdV StyledTextInput-bzOzsW laePbP"
           data-testid="test-select"
           id="test-select"
           multiple=""
@@ -2229,6 +2231,7 @@ exports[`Select size 1`] = `
   font-weight: 600;
   margin: 0;
   width: 100%;
+  -webkit-appearance: textfield;
   font-size: 24px;
   line-height: 1.167;
   border: none;
diff --git a/src/js/components/TextInput/StyledTextInput.js b/src/js/components/TextInput/StyledTextInput.js
index ac789c51201..380ce7229f9 100644
--- a/src/js/components/TextInput/StyledTextInput.js
+++ b/src/js/components/TextInput/StyledTextInput.js
@@ -22,6 +22,7 @@ const plainStyle = css`
 const StyledTextInput = styled.input`
   ${inputStyle}
   width: 100%;
+  -webkit-appearance: textfield;
 
   ${props => props.size && sizeStyle(props)}
   ${props => props.plain && plainStyle}
diff --git a/src/js/components/TextInput/__tests__/__snapshots__/TextInput-test.js.snap b/src/js/components/TextInput/__tests__/__snapshots__/TextInput-test.js.snap
index cdc9e083ae3..8f61c190cd0 100644
--- a/src/js/components/TextInput/__tests__/__snapshots__/TextInput-test.js.snap
+++ b/src/js/components/TextInput/__tests__/__snapshots__/TextInput-test.js.snap
@@ -6,7 +6,7 @@ exports[`TextInput basic 1`] = `
 >
   <input
     autocomplete="off"
-    class="StyledTextInput-bzOzsW kZINoq"
+    class="StyledTextInput-bzOzsW cowQZI"
     name="item"
     value=""
   />
@@ -22,7 +22,7 @@ exports[`TextInput close suggestion drop 1`] = `
   >
     <input
       autocomplete="off"
-      class="StyledTextInput-bzOzsW kZINoq"
+      class="StyledTextInput-bzOzsW cowQZI"
       data-testid="test-input"
       id="item"
       name="item"
@@ -130,7 +130,7 @@ exports[`TextInput close suggestion drop 4`] = `
   >
     <input
       autocomplete="off"
-      class="StyledTextInput-bzOzsW kZINoq"
+      class="StyledTextInput-bzOzsW cowQZI"
       data-testid="test-input"
       id="item"
       name="item"
@@ -149,7 +149,7 @@ exports[`TextInput complex suggestions 1`] = `
   >
     <input
       autocomplete="off"
-      class="StyledTextInput-bzOzsW kZINoq"
+      class="StyledTextInput-bzOzsW cowQZI"
       data-testid="test-input"
       id="item"
       name="item"
@@ -257,7 +257,7 @@ exports[`TextInput handles next and previous without suggestion 1`] = `
   >
     <input
       autocomplete="off"
-      class="StyledTextInput-bzOzsW kZINoq"
+      class="StyledTextInput-bzOzsW cowQZI"
       data-testid="test-input"
       id="item"
       name="item"
@@ -276,7 +276,7 @@ exports[`TextInput handles next and previous without suggestion 2`] = `
   >
     <input
       autocomplete="off"
-      class="StyledTextInput-bzOzsW kZINoq"
+      class="StyledTextInput-bzOzsW cowQZI"
       data-testid="test-input"
       id="item"
       name="item"
@@ -295,7 +295,7 @@ exports[`TextInput next and previous suggestions 1`] = `
   >
     <input
       autocomplete="off"
-      class="StyledTextInput-bzOzsW kZINoq"
+      class="StyledTextInput-bzOzsW cowQZI"
       data-testid="test-input"
       id="item"
       name="item"
@@ -314,7 +314,7 @@ exports[`TextInput select a suggestion 1`] = `
   >
     <input
       autocomplete="off"
-      class="StyledTextInput-bzOzsW kZINoq"
+      class="StyledTextInput-bzOzsW cowQZI"
       data-testid="test-input"
       id="item"
       name="item"
@@ -333,7 +333,7 @@ exports[`TextInput select suggestion 1`] = `
   >
     <input
       autocomplete="off"
-      class="StyledTextInput-bzOzsW dGDNWv"
+      class="StyledTextInput-bzOzsW chYNBg"
       data-testid="test-input"
       id="item"
       name="item"
@@ -441,7 +441,7 @@ exports[`TextInput select suggestion 4`] = `
   >
     <input
       autocomplete="off"
-      class="StyledTextInput-bzOzsW dGDNWv"
+      class="StyledTextInput-bzOzsW chYNBg"
       data-testid="test-input"
       id="item"
       name="item"
@@ -457,7 +457,7 @@ exports[`TextInput suggestions 1`] = `
 >
   <input
     autocomplete="off"
-    class="StyledTextInput-bzOzsW kZINoq"
+    class="StyledTextInput-bzOzsW cowQZI"
     data-testid="test-input"
     id="item"
     name="item"
