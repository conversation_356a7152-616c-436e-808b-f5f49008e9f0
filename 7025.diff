diff --git a/src/js/components/Data/Data.js b/src/js/components/Data/Data.js
index 8cc31e1066e..c6e34c4059c 100644
--- a/src/js/components/Data/Data.js
+++ b/src/js/components/Data/Data.js
@@ -28,7 +28,6 @@ export const Data = ({
   properties,
   toolbar,
   total,
-  updateOn = 'submit',
   view: viewProp,
   views,
   ...rest
@@ -53,6 +52,9 @@ export const Data = ({
     return filter(dataProp, view, properties);
   }, [dataProp, filteredTotal, onView, properties, total, view]);
 
+  // used by DataFilters to determine if badge should appear on Filter button
+  const [filtersCleared, setFiltersCleared] = useState(true);
+
   const announce = useContext(AnnounceContext);
   const { format } = useContext(MessageContext);
   // Announce to screen readers when search or filters are
@@ -96,7 +98,8 @@ export const Data = ({
       id,
       messages,
       properties,
-      updateOn,
+      filtersCleared,
+      setFiltersCleared,
       view,
       views,
       ...result,
@@ -104,6 +107,7 @@ export const Data = ({
 
     value.clearFilters = () => {
       const nextView = defaultView;
+      setFiltersCleared(true);
       setView(nextView);
       if (onView) onView(nextView);
     };
@@ -126,11 +130,11 @@ export const Data = ({
     defaultView,
     id,
     messages,
+    filtersCleared,
     onView,
     properties,
     result,
     toolbarKeys,
-    updateOn,
     view,
     views,
   ]);
diff --git a/src/js/components/Data/DataForm.js b/src/js/components/Data/DataForm.js
index daf60990592..6206da10667 100644
--- a/src/js/components/Data/DataForm.js
+++ b/src/js/components/Data/DataForm.js
@@ -258,17 +258,10 @@ export const DataForm = ({
   onDone,
   onTouched,
   pad,
-  updateOn: updateOnProp,
+  updateOn = 'submit',
   ...rest
 }) => {
-  const {
-    messages,
-    onView,
-    updateOn: updateOnData,
-    view,
-    views,
-  } = useContext(DataContext);
-  const updateOn = updateOnProp ?? updateOnData;
+  const { messages, onView, view, views } = useContext(DataContext);
   const { format } = useContext(MessageContext);
   const [formValue, setFormValue] = useState(viewToFormValue(view));
   const [changed, setChanged] = useState();
diff --git a/src/js/components/Data/__tests__/Data-test.tsx b/src/js/components/Data/__tests__/Data-test.tsx
index 6564fe207ca..9a783725ba8 100644
--- a/src/js/components/Data/__tests__/Data-test.tsx
+++ b/src/js/components/Data/__tests__/Data-test.tsx
@@ -244,7 +244,6 @@ describe('Data', () => {
           properties={{ name: { label: 'Name' } }}
           view={{ search: '', properties: {} }}
           toolbar
-          updateOn="change"
         >
           <DataTable />
         </Data>
@@ -276,7 +275,6 @@ describe('Data', () => {
           properties={{ name: { label: 'Name' } }}
           view={{ search: '', properties: {} }}
           toolbar
-          updateOn="change"
           onView={onView}
         >
           <DataTable />
diff --git a/src/js/components/Data/index.d.ts b/src/js/components/Data/index.d.ts
index fcbc8f5e9b8..d2ad42b3d97 100644
--- a/src/js/components/Data/index.d.ts
+++ b/src/js/components/Data/index.d.ts
@@ -35,8 +35,6 @@ export interface DataProps {
   defaultView?: View;
   view?: string | View;
   onView?: (view: View) => void;
-  // when view changes should be delivered
-  updateOn?: 'change' | 'submit';
 
   // whether to render a Toolbar
   toolbar?: boolean | 'search' | 'filters';
diff --git a/src/js/components/Data/propTypes.js b/src/js/components/Data/propTypes.js
index 1b56ed96f7b..e0a0375fcdc 100644
--- a/src/js/components/Data/propTypes.js
+++ b/src/js/components/Data/propTypes.js
@@ -27,7 +27,6 @@ if (process.env.NODE_ENV !== 'production') {
       PropTypes.oneOf(['search', 'filters']),
     ]),
     total: PropTypes.number,
-    updateOn: PropTypes.oneOf(['change', 'submit']),
     view: PropTypes.oneOfType([PropTypes.string, viewType]),
   };
 }
diff --git a/src/js/components/Data/stories/Inline.js b/src/js/components/Data/stories/Inline.js
index 8e93b9758d6..76b018cb590 100644
--- a/src/js/components/Data/stories/Inline.js
+++ b/src/js/components/Data/stories/Inline.js
@@ -58,7 +58,7 @@ export const Inline = () => {
       </Toolbar>
     );
   } else {
-    sidebar = <Filters search />;
+    sidebar = <Filters search updateOn="change" />;
   }
 
   return (
@@ -71,7 +71,6 @@ export const Inline = () => {
         date: { label: 'Date' },
       }}
       data={DATA}
-      updateOn={sidebar ? 'change' : undefined}
     >
       <Box pad={{ top: 'medium' }} align="center">
         <Notification
diff --git a/src/js/components/DataClearFilters/DataClearFilters.js b/src/js/components/DataClearFilters/DataClearFilters.js
new file mode 100644
index 00000000000..86c39d67094
--- /dev/null
+++ b/src/js/components/DataClearFilters/DataClearFilters.js
@@ -0,0 +1,33 @@
+import React, { forwardRef, useContext } from 'react';
+import { ThemeContext } from 'styled-components';
+import { DataClearFiltersPropTypes } from './propTypes';
+import { Button } from '../Button';
+import { DataContext } from '../../contexts/DataContext';
+import { MessageContext } from '../../contexts/MessageContext';
+
+const DataClearFilters = forwardRef(({ onClick, ...rest }, ref) => {
+  const theme = useContext(ThemeContext);
+  const { format } = useContext(MessageContext);
+  const { clearFilters, messages } = useContext(DataContext);
+
+  return (
+    <Button
+      ref={ref}
+      kind={theme.data.button?.kind}
+      label={format({
+        id: 'dataFilters.clear',
+        messages: messages?.dataFilters,
+      })}
+      onClick={(event) => {
+        clearFilters();
+        if (onClick) onClick(event);
+      }}
+      {...rest}
+    />
+  );
+});
+
+DataClearFilters.displayName = 'DataClearFilters';
+DataClearFilters.propTypes = DataClearFiltersPropTypes;
+
+export { DataClearFilters };
diff --git a/src/js/components/DataClearFilters/README.md b/src/js/components/DataClearFilters/README.md
new file mode 100644
index 00000000000..88cce50160b
--- /dev/null
+++ b/src/js/components/DataClearFilters/README.md
@@ -0,0 +1,2 @@
+## DataClearFilters
+Documentation for this component: https://v2.grommet.io/dataclearfilters
diff --git a/src/js/components/DataClearFilters/__tests__/DataClearFilters-test.tsx b/src/js/components/DataClearFilters/__tests__/DataClearFilters-test.tsx
new file mode 100644
index 00000000000..c9ef3e5ed4a
--- /dev/null
+++ b/src/js/components/DataClearFilters/__tests__/DataClearFilters-test.tsx
@@ -0,0 +1,89 @@
+import React from 'react';
+import { render, screen, fireEvent } from '@testing-library/react';
+import 'jest-styled-components';
+import { Data } from '../../Data';
+import { Grommet } from '../../Grommet';
+import { DataClearFilters } from '..';
+
+// asserts that AnnounceContext aria-live region and visible DataSummary each have this text
+const expectDataSummary = (message: string) =>
+  expect(screen.getAllByText(message)).toHaveLength(2);
+
+const data = [
+  {
+    name: 'aa',
+    enabled: true,
+    rating: 2.3,
+    type: { name: 'ZZ', id: 1 },
+    blank: '',
+    zero: 0,
+    total: 4,
+  },
+  {
+    name: 'bb',
+    enabled: false,
+    rating: 4.3,
+    type: { name: 'YY', id: 2 },
+    blank: '',
+    zero: 0,
+    total: 200,
+  },
+  { name: 'cc', type: { name: 'ZZ', id: 1 }, blank: '', zero: 0, total: 35 },
+];
+
+describe('DataClearFilters', () => {
+  test('renders', () => {
+    const { asFragment } = render(
+      <Grommet>
+        <Data data={data}>
+          <DataClearFilters />
+        </Data>
+      </Grommet>,
+    );
+
+    expect(asFragment()).toMatchSnapshot();
+  });
+
+  test('clears filters when clicked', () => {
+    const { asFragment } = render(
+      <Grommet>
+        <Data
+          data={data}
+          view={{
+            properties: {
+              name: ['cc'],
+            },
+          }}
+          toolbar
+        >
+          <DataClearFilters />
+        </Data>
+      </Grommet>,
+    );
+
+    fireEvent.click(screen.getByRole('button', { name: 'Clear filters' }));
+    expectDataSummary(`${data.length} items`);
+    expect(asFragment()).toMatchSnapshot();
+  });
+
+  test('renders custom message', () => {
+    const { asFragment } = render(
+      <Grommet>
+        <Data
+          data={data}
+          messages={{
+            dataFilters: {
+              clear: 'Remove all filters',
+            },
+          }}
+          toolbar
+        >
+          <DataClearFilters />
+        </Data>
+      </Grommet>,
+    );
+
+    expect(screen.getByText('Remove all filters')).toBeTruthy();
+    expect(asFragment()).toMatchSnapshot();
+  });
+});
diff --git a/src/js/components/DataClearFilters/__tests__/__snapshots__/DataClearFilters-test.tsx.snap b/src/js/components/DataClearFilters/__tests__/__snapshots__/DataClearFilters-test.tsx.snap
new file mode 100644
index 00000000000..55b508630ae
--- /dev/null
+++ b/src/js/components/DataClearFilters/__tests__/__snapshots__/DataClearFilters-test.tsx.snap
@@ -0,0 +1,981 @@
+// Jest Snapshot v1, https://goo.gl/fbAQLP
+
+exports[`DataClearFilters clears filters when clicked 1`] = `
+<DocumentFragment>
+  .c6 {
+  display: inline-block;
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+  width: 24px;
+  height: 24px;
+  fill: #666666;
+  stroke: #666666;
+}
+
+.c6 g {
+  fill: inherit;
+  stroke: inherit;
+}
+
+.c6 *:not([stroke])[fill='none'] {
+  stroke-width: 0;
+}
+
+.c6 *[stroke*='#'],
+.c6 *[STROKE*='#'] {
+  stroke: inherit;
+  fill: none;
+}
+
+.c6 *[fill-rule],
+.c6 *[FILL-RULE],
+.c6 *[fill*='#'],
+.c6 *[FILL*='#'] {
+  fill: inherit;
+  stroke: none;
+}
+
+.c1 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: column;
+  -ms-flex-direction: column;
+  flex-direction: column;
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+}
+
+.c8 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: row;
+  -ms-flex-direction: row;
+  flex-direction: row;
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+}
+
+.c2 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  -webkit-align-items: flex-start;
+  -webkit-box-align: flex-start;
+  -ms-flex-align: flex-start;
+  align-items: flex-start;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: row;
+  -ms-flex-direction: row;
+  flex-direction: row;
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+  -webkit-flex-wrap: wrap;
+  -ms-flex-wrap: wrap;
+  flex-wrap: wrap;
+  -webkit-column-gap: 12px;
+  column-gap: 12px;
+  row-gap: 12px;
+}
+
+.c10 {
+  margin-top: 6px;
+  margin-bottom: 6px;
+  font-size: 18px;
+  line-height: 24px;
+}
+
+.c9 {
+  display: inline-block;
+  box-sizing: border-box;
+  cursor: pointer;
+  font: inherit;
+  -webkit-text-decoration: none;
+  text-decoration: none;
+  margin: 0;
+  background: transparent;
+  overflow: visible;
+  text-transform: none;
+  color: inherit;
+  outline: none;
+  border: none;
+  padding: 0;
+  text-align: inherit;
+  line-height: 0;
+  padding: 12px;
+}
+
+.c9:hover {
+  background-color: rgba(221,221,221,0.4);
+  color: #000000;
+}
+
+.c9:focus {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c9:focus > circle,
+.c9:focus > ellipse,
+.c9:focus > line,
+.c9:focus > path,
+.c9:focus > polygon,
+.c9:focus > polyline,
+.c9:focus > rect {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c9:focus::-moz-focus-inner {
+  border: 0;
+}
+
+.c9:focus:not(:focus-visible) {
+  outline: none;
+  box-shadow: none;
+}
+
+.c9:focus:not(:focus-visible) > circle,
+.c9:focus:not(:focus-visible) > ellipse,
+.c9:focus:not(:focus-visible) > line,
+.c9:focus:not(:focus-visible) > path,
+.c9:focus:not(:focus-visible) > polygon,
+.c9:focus:not(:focus-visible) > polyline,
+.c9:focus:not(:focus-visible) > rect {
+  outline: none;
+  box-shadow: none;
+}
+
+.c9:focus:not(:focus-visible)::-moz-focus-inner {
+  border: 0;
+}
+
+.c11 {
+  display: inline-block;
+  box-sizing: border-box;
+  cursor: pointer;
+  font: inherit;
+  -webkit-text-decoration: none;
+  text-decoration: none;
+  margin: 0;
+  background: transparent;
+  overflow: visible;
+  text-transform: none;
+  border: 2px solid #7D4CDB;
+  border-radius: 18px;
+  color: #444444;
+  padding: 4px 22px;
+  font-size: 18px;
+  line-height: 24px;
+  -webkit-transition-property: color,background-color,border-color,box-shadow;
+  transition-property: color,background-color,border-color,box-shadow;
+  -webkit-transition-duration: 0.1s;
+  transition-duration: 0.1s;
+  -webkit-transition-timing-function: ease-in-out;
+  transition-timing-function: ease-in-out;
+}
+
+.c11:hover {
+  box-shadow: 0px 0px 0px 2px #7D4CDB;
+}
+
+.c11:focus {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c11:focus > circle,
+.c11:focus > ellipse,
+.c11:focus > line,
+.c11:focus > path,
+.c11:focus > polygon,
+.c11:focus > polyline,
+.c11:focus > rect {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c11:focus::-moz-focus-inner {
+  border: 0;
+}
+
+.c11:focus:not(:focus-visible) {
+  outline: none;
+  box-shadow: none;
+}
+
+.c11:focus:not(:focus-visible) > circle,
+.c11:focus:not(:focus-visible) > ellipse,
+.c11:focus:not(:focus-visible) > line,
+.c11:focus:not(:focus-visible) > path,
+.c11:focus:not(:focus-visible) > polygon,
+.c11:focus:not(:focus-visible) > polyline,
+.c11:focus:not(:focus-visible) > rect {
+  outline: none;
+  box-shadow: none;
+}
+
+.c11:focus:not(:focus-visible)::-moz-focus-inner {
+  border: 0;
+}
+
+.c3 {
+  max-width: 100%;
+}
+
+.c7 {
+  box-sizing: border-box;
+  font-size: inherit;
+  font-family: inherit;
+  border: none;
+  -webkit-appearance: none;
+  background: transparent;
+  color: inherit;
+  width: 100%;
+  padding: 11px;
+  font-weight: 600;
+  margin: 0;
+  border: 1px solid rgba(0,0,0,0.33);
+  border-radius: 4px;
+  padding-left: 48px;
+}
+
+.c7:focus {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c7:focus > circle,
+.c7:focus > ellipse,
+.c7:focus > line,
+.c7:focus > path,
+.c7:focus > polygon,
+.c7:focus > polyline,
+.c7:focus > rect {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c7:focus::-moz-focus-inner {
+  border: 0;
+}
+
+.c7::-webkit-input-placeholder {
+  color: #AAAAAA;
+}
+
+.c7::-moz-placeholder {
+  color: #AAAAAA;
+}
+
+.c7:-ms-input-placeholder {
+  color: #AAAAAA;
+}
+
+.c7::-webkit-search-decoration {
+  -webkit-appearance: none;
+}
+
+.c7::-moz-focus-inner {
+  border: none;
+  outline: none;
+}
+
+.c7:-moz-placeholder,
+.c7::-moz-placeholder {
+  opacity: 1;
+}
+
+.c4 {
+  position: relative;
+  width: 100%;
+}
+
+.c5 {
+  position: absolute;
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  -webkit-box-packjustify: center;
+  -webkit-justify: center;
+  -ms-flex-packjustify: center;
+  justify: center;
+  top: 50%;
+  -webkit-transform: translateY(-50%);
+  -ms-transform: translateY(-50%);
+  transform: translateY(-50%);
+  pointer-events: none;
+  left: 12px;
+}
+
+.c0 {
+  font-size: 18px;
+  line-height: 24px;
+  box-sizing: border-box;
+  -webkit-text-size-adjust: 100%;
+  -ms-text-size-adjust: 100%;
+  -moz-osx-font-smoothing: grayscale;
+  -webkit-font-smoothing: antialiased;
+}
+
+@media only screen and (max-width:768px) {
+
+}
+
+@media only screen and (max-width:768px) {
+  .c2 {
+    -webkit-column-gap: 6px;
+    column-gap: 6px;
+  }
+}
+
+<div
+    class="c0"
+  >
+    <div
+      class="c1"
+      id="data"
+    >
+      <div
+        class="c2"
+      >
+        <form
+          class="c3"
+        >
+          <div
+            class="c4"
+          >
+            <div
+              class="c5"
+            >
+              <svg
+                aria-label="Search"
+                class="c6"
+                viewBox="0 0 24 24"
+              >
+                <path
+                  d="m15 15 7 7-7-7zm-5.5 2a7.5 7.5 0 1 0 0-15 7.5 7.5 0 0 0 0 15z"
+                  fill="none"
+                  stroke="#000"
+                  stroke-width="2"
+                />
+              </svg>
+            </div>
+            <input
+              aria-label="Search"
+              autocomplete="off"
+              class="c7"
+              id="data--search"
+              name="_search"
+              type="search"
+              value=""
+            />
+          </div>
+        </form>
+        <div
+          class="c8"
+        >
+          <button
+            aria-label="Open filters"
+            class="c9"
+            id="data--filters-control"
+            type="button"
+          >
+            <svg
+              aria-label="Filter"
+              class="c6"
+              viewBox="0 0 24 24"
+            >
+              <path
+                d="m3 6 7 7v8h4v-8l7-7V3H3z"
+                fill="none"
+                stroke="#000"
+                stroke-width="2"
+              />
+            </svg>
+          </button>
+        </div>
+      </div>
+      <span
+        class="c10"
+      >
+        3 items
+      </span>
+      <button
+        class="c11"
+        type="button"
+      >
+        Clear filters
+      </button>
+    </div>
+  </div>
+</DocumentFragment>
+`;
+
+exports[`DataClearFilters renders 1`] = `
+<DocumentFragment>
+  .c1 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: column;
+  -ms-flex-direction: column;
+  flex-direction: column;
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+}
+
+.c2 {
+  display: inline-block;
+  box-sizing: border-box;
+  cursor: pointer;
+  font: inherit;
+  -webkit-text-decoration: none;
+  text-decoration: none;
+  margin: 0;
+  background: transparent;
+  overflow: visible;
+  text-transform: none;
+  border: 2px solid #7D4CDB;
+  border-radius: 18px;
+  color: #444444;
+  padding: 4px 22px;
+  font-size: 18px;
+  line-height: 24px;
+  -webkit-transition-property: color,background-color,border-color,box-shadow;
+  transition-property: color,background-color,border-color,box-shadow;
+  -webkit-transition-duration: 0.1s;
+  transition-duration: 0.1s;
+  -webkit-transition-timing-function: ease-in-out;
+  transition-timing-function: ease-in-out;
+}
+
+.c2:hover {
+  box-shadow: 0px 0px 0px 2px #7D4CDB;
+}
+
+.c2:focus {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c2:focus > circle,
+.c2:focus > ellipse,
+.c2:focus > line,
+.c2:focus > path,
+.c2:focus > polygon,
+.c2:focus > polyline,
+.c2:focus > rect {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c2:focus::-moz-focus-inner {
+  border: 0;
+}
+
+.c2:focus:not(:focus-visible) {
+  outline: none;
+  box-shadow: none;
+}
+
+.c2:focus:not(:focus-visible) > circle,
+.c2:focus:not(:focus-visible) > ellipse,
+.c2:focus:not(:focus-visible) > line,
+.c2:focus:not(:focus-visible) > path,
+.c2:focus:not(:focus-visible) > polygon,
+.c2:focus:not(:focus-visible) > polyline,
+.c2:focus:not(:focus-visible) > rect {
+  outline: none;
+  box-shadow: none;
+}
+
+.c2:focus:not(:focus-visible)::-moz-focus-inner {
+  border: 0;
+}
+
+.c0 {
+  font-size: 18px;
+  line-height: 24px;
+  box-sizing: border-box;
+  -webkit-text-size-adjust: 100%;
+  -ms-text-size-adjust: 100%;
+  -moz-osx-font-smoothing: grayscale;
+  -webkit-font-smoothing: antialiased;
+}
+
+<div
+    class="c0"
+  >
+    <div
+      class="c1"
+      id="data"
+    >
+      <button
+        class="c2"
+        type="button"
+      >
+        Clear filters
+      </button>
+    </div>
+  </div>
+</DocumentFragment>
+`;
+
+exports[`DataClearFilters renders custom message 1`] = `
+<DocumentFragment>
+  .c6 {
+  display: inline-block;
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+  width: 24px;
+  height: 24px;
+  fill: #666666;
+  stroke: #666666;
+}
+
+.c6 g {
+  fill: inherit;
+  stroke: inherit;
+}
+
+.c6 *:not([stroke])[fill='none'] {
+  stroke-width: 0;
+}
+
+.c6 *[stroke*='#'],
+.c6 *[STROKE*='#'] {
+  stroke: inherit;
+  fill: none;
+}
+
+.c6 *[fill-rule],
+.c6 *[FILL-RULE],
+.c6 *[fill*='#'],
+.c6 *[FILL*='#'] {
+  fill: inherit;
+  stroke: none;
+}
+
+.c1 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: column;
+  -ms-flex-direction: column;
+  flex-direction: column;
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+}
+
+.c8 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: row;
+  -ms-flex-direction: row;
+  flex-direction: row;
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+}
+
+.c2 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  -webkit-align-items: flex-start;
+  -webkit-box-align: flex-start;
+  -ms-flex-align: flex-start;
+  align-items: flex-start;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: row;
+  -ms-flex-direction: row;
+  flex-direction: row;
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+  -webkit-flex-wrap: wrap;
+  -ms-flex-wrap: wrap;
+  flex-wrap: wrap;
+  -webkit-column-gap: 12px;
+  column-gap: 12px;
+  row-gap: 12px;
+}
+
+.c10 {
+  margin-top: 6px;
+  margin-bottom: 6px;
+  font-size: 18px;
+  line-height: 24px;
+}
+
+.c9 {
+  display: inline-block;
+  box-sizing: border-box;
+  cursor: pointer;
+  font: inherit;
+  -webkit-text-decoration: none;
+  text-decoration: none;
+  margin: 0;
+  background: transparent;
+  overflow: visible;
+  text-transform: none;
+  color: inherit;
+  outline: none;
+  border: none;
+  padding: 0;
+  text-align: inherit;
+  line-height: 0;
+  padding: 12px;
+}
+
+.c9:hover {
+  background-color: rgba(221,221,221,0.4);
+  color: #000000;
+}
+
+.c9:focus {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c9:focus > circle,
+.c9:focus > ellipse,
+.c9:focus > line,
+.c9:focus > path,
+.c9:focus > polygon,
+.c9:focus > polyline,
+.c9:focus > rect {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c9:focus::-moz-focus-inner {
+  border: 0;
+}
+
+.c9:focus:not(:focus-visible) {
+  outline: none;
+  box-shadow: none;
+}
+
+.c9:focus:not(:focus-visible) > circle,
+.c9:focus:not(:focus-visible) > ellipse,
+.c9:focus:not(:focus-visible) > line,
+.c9:focus:not(:focus-visible) > path,
+.c9:focus:not(:focus-visible) > polygon,
+.c9:focus:not(:focus-visible) > polyline,
+.c9:focus:not(:focus-visible) > rect {
+  outline: none;
+  box-shadow: none;
+}
+
+.c9:focus:not(:focus-visible)::-moz-focus-inner {
+  border: 0;
+}
+
+.c11 {
+  display: inline-block;
+  box-sizing: border-box;
+  cursor: pointer;
+  font: inherit;
+  -webkit-text-decoration: none;
+  text-decoration: none;
+  margin: 0;
+  background: transparent;
+  overflow: visible;
+  text-transform: none;
+  border: 2px solid #7D4CDB;
+  border-radius: 18px;
+  color: #444444;
+  padding: 4px 22px;
+  font-size: 18px;
+  line-height: 24px;
+  -webkit-transition-property: color,background-color,border-color,box-shadow;
+  transition-property: color,background-color,border-color,box-shadow;
+  -webkit-transition-duration: 0.1s;
+  transition-duration: 0.1s;
+  -webkit-transition-timing-function: ease-in-out;
+  transition-timing-function: ease-in-out;
+}
+
+.c11:hover {
+  box-shadow: 0px 0px 0px 2px #7D4CDB;
+}
+
+.c11:focus {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c11:focus > circle,
+.c11:focus > ellipse,
+.c11:focus > line,
+.c11:focus > path,
+.c11:focus > polygon,
+.c11:focus > polyline,
+.c11:focus > rect {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c11:focus::-moz-focus-inner {
+  border: 0;
+}
+
+.c11:focus:not(:focus-visible) {
+  outline: none;
+  box-shadow: none;
+}
+
+.c11:focus:not(:focus-visible) > circle,
+.c11:focus:not(:focus-visible) > ellipse,
+.c11:focus:not(:focus-visible) > line,
+.c11:focus:not(:focus-visible) > path,
+.c11:focus:not(:focus-visible) > polygon,
+.c11:focus:not(:focus-visible) > polyline,
+.c11:focus:not(:focus-visible) > rect {
+  outline: none;
+  box-shadow: none;
+}
+
+.c11:focus:not(:focus-visible)::-moz-focus-inner {
+  border: 0;
+}
+
+.c3 {
+  max-width: 100%;
+}
+
+.c7 {
+  box-sizing: border-box;
+  font-size: inherit;
+  font-family: inherit;
+  border: none;
+  -webkit-appearance: none;
+  background: transparent;
+  color: inherit;
+  width: 100%;
+  padding: 11px;
+  font-weight: 600;
+  margin: 0;
+  border: 1px solid rgba(0,0,0,0.33);
+  border-radius: 4px;
+  padding-left: 48px;
+}
+
+.c7:focus {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c7:focus > circle,
+.c7:focus > ellipse,
+.c7:focus > line,
+.c7:focus > path,
+.c7:focus > polygon,
+.c7:focus > polyline,
+.c7:focus > rect {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c7:focus::-moz-focus-inner {
+  border: 0;
+}
+
+.c7::-webkit-input-placeholder {
+  color: #AAAAAA;
+}
+
+.c7::-moz-placeholder {
+  color: #AAAAAA;
+}
+
+.c7:-ms-input-placeholder {
+  color: #AAAAAA;
+}
+
+.c7::-webkit-search-decoration {
+  -webkit-appearance: none;
+}
+
+.c7::-moz-focus-inner {
+  border: none;
+  outline: none;
+}
+
+.c7:-moz-placeholder,
+.c7::-moz-placeholder {
+  opacity: 1;
+}
+
+.c4 {
+  position: relative;
+  width: 100%;
+}
+
+.c5 {
+  position: absolute;
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  -webkit-box-packjustify: center;
+  -webkit-justify: center;
+  -ms-flex-packjustify: center;
+  justify: center;
+  top: 50%;
+  -webkit-transform: translateY(-50%);
+  -ms-transform: translateY(-50%);
+  transform: translateY(-50%);
+  pointer-events: none;
+  left: 12px;
+}
+
+.c0 {
+  font-size: 18px;
+  line-height: 24px;
+  box-sizing: border-box;
+  -webkit-text-size-adjust: 100%;
+  -ms-text-size-adjust: 100%;
+  -moz-osx-font-smoothing: grayscale;
+  -webkit-font-smoothing: antialiased;
+}
+
+@media only screen and (max-width:768px) {
+
+}
+
+@media only screen and (max-width:768px) {
+  .c2 {
+    -webkit-column-gap: 6px;
+    column-gap: 6px;
+  }
+}
+
+<div
+    class="c0"
+  >
+    <div
+      class="c1"
+      id="data"
+    >
+      <div
+        class="c2"
+      >
+        <form
+          class="c3"
+        >
+          <div
+            class="c4"
+          >
+            <div
+              class="c5"
+            >
+              <svg
+                aria-label="Search"
+                class="c6"
+                viewBox="0 0 24 24"
+              >
+                <path
+                  d="m15 15 7 7-7-7zm-5.5 2a7.5 7.5 0 1 0 0-15 7.5 7.5 0 0 0 0 15z"
+                  fill="none"
+                  stroke="#000"
+                  stroke-width="2"
+                />
+              </svg>
+            </div>
+            <input
+              aria-label="Search"
+              autocomplete="off"
+              class="c7"
+              id="data--search"
+              name="_search"
+              type="search"
+              value=""
+            />
+          </div>
+        </form>
+        <div
+          class="c8"
+        >
+          <button
+            aria-label="Open filters"
+            class="c9"
+            id="data--filters-control"
+            type="button"
+          >
+            <svg
+              aria-label="Filter"
+              class="c6"
+              viewBox="0 0 24 24"
+            >
+              <path
+                d="m3 6 7 7v8h4v-8l7-7V3H3z"
+                fill="none"
+                stroke="#000"
+                stroke-width="2"
+              />
+            </svg>
+          </button>
+        </div>
+      </div>
+      <span
+        class="c10"
+      >
+        3 items
+      </span>
+      <button
+        class="c11"
+        type="button"
+      >
+        Remove all filters
+      </button>
+    </div>
+  </div>
+</DocumentFragment>
+`;
diff --git a/src/js/components/DataClearFilters/index.d.ts b/src/js/components/DataClearFilters/index.d.ts
new file mode 100644
index 00000000000..5e9a61fd59d
--- /dev/null
+++ b/src/js/components/DataClearFilters/index.d.ts
@@ -0,0 +1,6 @@
+import * as React from 'react';
+import { ButtonExtendedProps } from '../Button';
+
+declare const DataClearFilters: React.FC<ButtonExtendedProps>;
+
+export { DataClearFilters };
diff --git a/src/js/components/DataClearFilters/index.js b/src/js/components/DataClearFilters/index.js
new file mode 100644
index 00000000000..cc4939e1bd9
--- /dev/null
+++ b/src/js/components/DataClearFilters/index.js
@@ -0,0 +1 @@
+export { DataClearFilters } from './DataClearFilters';
diff --git a/src/js/components/DataClearFilters/propTypes.js b/src/js/components/DataClearFilters/propTypes.js
new file mode 100644
index 00000000000..0c9ea280dff
--- /dev/null
+++ b/src/js/components/DataClearFilters/propTypes.js
@@ -0,0 +1,3 @@
+import { ButtonPropTypes } from '../Button/propTypes';
+
+export const DataClearFiltersPropTypes = ButtonPropTypes;
diff --git a/src/js/components/DataClearFilters/stories/Simple.js b/src/js/components/DataClearFilters/stories/Simple.js
new file mode 100644
index 00000000000..d42309486c3
--- /dev/null
+++ b/src/js/components/DataClearFilters/stories/Simple.js
@@ -0,0 +1,67 @@
+import React, { useContext } from 'react';
+
+import {
+  Box,
+  Data,
+  DataClearFilters,
+  DataContext,
+  DataFilter,
+  DataFilters,
+  DataSearch,
+  DataSummary,
+  DataTable,
+  SelectMultiple,
+  Toolbar,
+} from 'grommet';
+import { columns, DATA } from '../../DataTable/stories/data';
+
+export const Simple = () => (
+  // Uncomment <Grommet> lines when using outside of storybook
+  // <Grommet theme={...}>
+  <Box pad="medium">
+    <Data
+      data={DATA}
+      total={DATA.length}
+      properties={{
+        location: { label: 'Location' },
+      }}
+    >
+      <DataToolbar />
+      <DataSummary />
+      <DataTable columns={columns} />
+    </Data>
+  </Box>
+  // </Grommet>
+);
+
+const DataToolbar = () => {
+  const { filteredTotal, total } = useContext(DataContext);
+
+  return (
+    <Toolbar align="end">
+      <DataSearch placeholder="Search" />
+      <DataFilters updateOn="change">
+        <DataFilter
+          property="location"
+          // override HPE theme margin to align with search + filter
+          contentProps={{ margin: { bottom: 'none', top: 'xsmall' } }}
+          // override Grommet theme margin to align with search + filter
+          margin="none"
+        >
+          <SelectMultiple
+            placeholder="Select location"
+            options={['Boise', 'Fort Collins', 'Palo Alto', 'San Francisco']}
+            name="location"
+          />
+        </DataFilter>
+      </DataFilters>
+      {filteredTotal !== total ? <DataClearFilters /> : null}
+    </Toolbar>
+  );
+};
+
+Simple.storyName = 'Simple';
+
+export default {
+  title: 'Data/DataClearFilters/Simple',
+};
diff --git a/src/js/components/DataFilters/DataFilters.js b/src/js/components/DataFilters/DataFilters.js
index 204c708d51b..6a8a8c68c4b 100644
--- a/src/js/components/DataFilters/DataFilters.js
+++ b/src/js/components/DataFilters/DataFilters.js
@@ -1,8 +1,16 @@
-import React, { Children, useContext, useMemo, useState } from 'react';
+import React, {
+  Children,
+  useContext,
+  useEffect,
+  useMemo,
+  useState,
+} from 'react';
 import { Filter } from 'grommet-icons/icons/Filter';
 import { Close } from 'grommet-icons/icons/Close';
+import { ThemeContext } from 'styled-components';
 import { Box } from '../Box';
 import { Button } from '../Button';
+import { DataClearFilters } from '../DataClearFilters';
 import { DataFilter } from '../DataFilter';
 import { DataForm } from '../Data/DataForm';
 import { DataSort } from '../DataSort';
@@ -23,24 +31,52 @@ const layerProps = {
   position: 'right',
 };
 
-export const DataFilters = ({ drop, children, heading, layer, ...rest }) => {
+const defaultTouched = {};
+export const DataFilters = ({
+  drop,
+  children,
+  clearFilters = true,
+  heading,
+  layer,
+  updateOn,
+  ...rest
+}) => {
   const {
-    clearFilters,
     id: dataId,
     messages,
     properties,
     unfilteredData,
+    filtersCleared,
+    setFiltersCleared,
     view,
   } = useContext(DataContext);
   const { format } = useContext(MessageContext);
+  const theme = useContext(ThemeContext);
   const [showContent, setShowContent] = useState();
   // touched is a map of form field name to its value, it only has fields that
   // were changed as part of the DataForm here. This is so we can track based
   // on what's inside DataFilters as opposed to trying to track from the view
-  // object.
-  const [touched, setTouched] = useState({});
+  // object, since touched is used as logic for whether to show badge or not
+  const [touched, setTouched] = useState(defaultTouched);
+
+  // if filters have been applied by this DataFilters, update
+  // the DataContext that filters are not in a "cleared" state
+  useEffect(() => {
+    setFiltersCleared(!Object.keys(touched).length);
+  }, [touched, setFiltersCleared]);
+
+  // if filters have been cleared via clearFilters in DataContext,
+  // reset touched to default state so badge is removed
+  useEffect(() => {
+    if (filtersCleared) {
+      setTouched(defaultTouched);
+    }
+  }, [filtersCleared]);
   const controlled = useMemo(() => drop || layer, [drop, layer]);
-  // generate the badge value based on touched fields that have a value
+
+  // generate the badge value based on touched fields that have a value.
+  // only show the badge based off of what's included in this DataFilters
+  // since multiple DataFilters may exist
   const badge = useMemo(
     () =>
       (controlled && Object.keys(touched).filter((k) => touched[k]).length) ||
@@ -48,18 +84,9 @@ export const DataFilters = ({ drop, children, heading, layer, ...rest }) => {
     [controlled, touched],
   );
 
-  const clearControl = badge && (
+  const clearControl = badge && clearFilters && (
     <Box flex={false} margin={{ start: 'small' }}>
-      <Button
-        label={format({
-          id: 'dataFilters.clear',
-          messages: messages?.dataFilters,
-        })}
-        onClick={() => {
-          setTouched({});
-          clearFilters();
-        }}
-      />
+      <DataClearFilters />
     </Box>
   );
 
@@ -99,6 +126,7 @@ export const DataFilters = ({ drop, children, heading, layer, ...rest }) => {
               }))
           : undefined
       }
+      updateOn={updateOn}
       {...(!controlled ? rest : { fill: 'vertical' })}
     >
       {layer && (
@@ -139,7 +167,7 @@ export const DataFilters = ({ drop, children, heading, layer, ...rest }) => {
         id={`${dataId}--filters-control`}
         tip={tip}
         aria-label={tip}
-        kind="toolbar"
+        kind={theme.data.button?.kind}
         icon={<Filter />}
         hoverIndicator
         dropProps={dropProps}
@@ -156,7 +184,7 @@ export const DataFilters = ({ drop, children, heading, layer, ...rest }) => {
         id={`${dataId}--filters-control`}
         tip={tip}
         aria-label={tip}
-        kind="toolbar"
+        kind={theme.data.button?.kind}
         hoverIndicator
         icon={<Filter />}
         badge={badge}
diff --git a/src/js/components/DataFilters/index.d.ts b/src/js/components/DataFilters/index.d.ts
index 9d73e716fa6..3d33fc8afdb 100644
--- a/src/js/components/DataFilters/index.d.ts
+++ b/src/js/components/DataFilters/index.d.ts
@@ -5,6 +5,9 @@ export interface DataFiltersProps {
   drop?: boolean;
   heading?: string | React.ReactNode;
   layer?: boolean;
+  clearFilters?: boolean;
+  // when view changes should be delivered
+  updateOn?: 'change' | 'submit';
 }
 
 type divProps = Omit<JSX.IntrinsicElements['div'], 'onClick'>;
diff --git a/src/js/components/DataFilters/propTypes.js b/src/js/components/DataFilters/propTypes.js
index 751b7245982..6610d411023 100644
--- a/src/js/components/DataFilters/propTypes.js
+++ b/src/js/components/DataFilters/propTypes.js
@@ -6,6 +6,8 @@ if (process.env.NODE_ENV !== 'production') {
     drop: PropTypes.bool,
     heading: PropTypes.string,
     layer: PropTypes.bool,
+    clearFilters: PropTypes.bool,
+    updateOn: PropTypes.oneOf(['change', 'submit']),
   };
 }
 export const DataFiltersPropTypes = PropType;
diff --git a/src/js/components/DataSearch/DataSearch.js b/src/js/components/DataSearch/DataSearch.js
index dd608fbb1cd..d0a29d07865 100644
--- a/src/js/components/DataSearch/DataSearch.js
+++ b/src/js/components/DataSearch/DataSearch.js
@@ -1,5 +1,6 @@
 import React, { useContext, useEffect, useState } from 'react';
 import { Search } from 'grommet-icons/icons/Search';
+import { ThemeContext } from 'styled-components';
 import { Box } from '../Box';
 import { DataContext } from '../../contexts/DataContext';
 import { DataForm } from '../Data/DataForm';
@@ -21,6 +22,7 @@ export const DataSearch = ({ drop, id: idProp, responsive, ...rest }) => {
   const { id: dataId, messages, addToolbarKey } = useContext(DataContext);
   const { noForm } = useContext(FormContext);
   const { format } = useContext(MessageContext);
+  const theme = useContext(ThemeContext);
   const size = useContext(ResponsiveContext);
   const skeleton = useSkeleton();
   const [showContent, setShowContent] = useState();
@@ -73,7 +75,7 @@ export const DataSearch = ({ drop, id: idProp, responsive, ...rest }) => {
         id: 'dataSearch.open',
         messages: messages?.dataSort,
       })}
-      kind="toolbar"
+      kind={theme.data.button?.kind}
       icon={<Search />}
       dropProps={dropProps}
       dropContent={<Box pad="small">{content}</Box>}
diff --git a/src/js/components/DataSearch/stories/Drop.js b/src/js/components/DataSearch/stories/Drop.js
index cb34be6d0e3..05778a8b051 100644
--- a/src/js/components/DataSearch/stories/Drop.js
+++ b/src/js/components/DataSearch/stories/Drop.js
@@ -23,7 +23,7 @@ export const Drop = () => (
     <Paragraph color="text-weak">
       Note: Results are filtered as you type, checking all fields.
     </Paragraph>
-    <Data data={DATA} updateOn="change">
+    <Data data={DATA}>
       <DataSearch drop />
       <DataSummary />
       <DataTable columns={columns} />
diff --git a/src/js/components/DataSearch/stories/Responsive.js b/src/js/components/DataSearch/stories/Responsive.js
index 01a58a8d46d..b4ad58709e0 100644
--- a/src/js/components/DataSearch/stories/Responsive.js
+++ b/src/js/components/DataSearch/stories/Responsive.js
@@ -23,7 +23,7 @@ export const Responsive = () => (
     <Paragraph color="text-weak">
       Note: Results are filtered as you type, checking all fields.
     </Paragraph>
-    <Data data={DATA} updateOn="change">
+    <Data data={DATA}>
       <DataSearch responsive />
       <DataSummary />
       <DataTable columns={columns} />
diff --git a/src/js/components/DataSearch/stories/Simple.js b/src/js/components/DataSearch/stories/Simple.js
index 5e410f46973..ddec58fac4b 100644
--- a/src/js/components/DataSearch/stories/Simple.js
+++ b/src/js/components/DataSearch/stories/Simple.js
@@ -23,7 +23,7 @@ export const Simple = () => (
     <Paragraph color="text-weak">
       Note: Results are filtered as you type, checking all fields.
     </Paragraph>
-    <Data data={DATA} updateOn="change">
+    <Data data={DATA}>
       <DataSearch />
       <DataSummary />
       <DataTable columns={columns} />
diff --git a/src/js/components/DataSort/DataSort.js b/src/js/components/DataSort/DataSort.js
index 79a7980ad44..70cdc528a0b 100644
--- a/src/js/components/DataSort/DataSort.js
+++ b/src/js/components/DataSort/DataSort.js
@@ -1,5 +1,6 @@
 import React, { useContext, useMemo, useState } from 'react';
 import { Descend } from 'grommet-icons/icons/Descend';
+import { ThemeContext } from 'styled-components';
 import { DataContext } from '../../contexts/DataContext';
 import { Box } from '../Box';
 import { DataForm } from '../Data/DataForm';
@@ -80,11 +81,17 @@ export const DataSort = ({ drop, options, ...rest }) => {
   const { id: dataId, messages } = useContext(DataContext);
   const { noForm } = useContext(FormContext);
   const { format } = useContext(MessageContext);
+  const theme = useContext(ThemeContext);
   const [showContent, setShowContent] = useState();
 
   let content = <Content options={options} />;
 
-  if (noForm) content = <DataForm footer={false}>{content}</DataForm>;
+  if (noForm)
+    content = (
+      <DataForm footer={false} updateOn="change">
+        {content}
+      </DataForm>
+    );
 
   if (!drop) return content;
 
@@ -95,7 +102,7 @@ export const DataSort = ({ drop, options, ...rest }) => {
         id: 'dataSort.open',
         messages: messages?.dataSort,
       })}
-      kind="toolbar"
+      kind={theme.data.button?.kind}
       icon={<Descend />}
       dropProps={dropProps}
       dropContent={<Box pad="small">{content}</Box>}
diff --git a/src/js/components/DataSort/stories/Drop.js b/src/js/components/DataSort/stories/Drop.js
index ba00728b90b..18fce31d84c 100644
--- a/src/js/components/DataSort/stories/Drop.js
+++ b/src/js/components/DataSort/stories/Drop.js
@@ -13,7 +13,7 @@ export const Drop = () => (
       status="info"
       message="Data is in 'beta'. The API surface is subject to change."
     />
-    <Data data={DATA} updateOn="change">
+    <Data data={DATA}>
       <Toolbar>
         <DataSort drop />
       </Toolbar>
diff --git a/src/js/components/DataSort/stories/Simple.js b/src/js/components/DataSort/stories/Simple.js
index d3239c3cc69..48eccfa15a8 100644
--- a/src/js/components/DataSort/stories/Simple.js
+++ b/src/js/components/DataSort/stories/Simple.js
@@ -13,7 +13,7 @@ export const Simple = () => (
       status="info"
       message="Data is in 'beta'. The API surface is subject to change."
     />
-    <Data data={DATA} updateOn="change">
+    <Data data={DATA}>
       <DataSort />
       <DataTable columns={columns} />
     </Data>
diff --git a/src/js/components/DataTableColumns/DataTableColumns.js b/src/js/components/DataTableColumns/DataTableColumns.js
index 7e3f3dac137..d9d91ad248e 100644
--- a/src/js/components/DataTableColumns/DataTableColumns.js
+++ b/src/js/components/DataTableColumns/DataTableColumns.js
@@ -1,6 +1,7 @@
 import React, { useCallback, useContext, useMemo, useState } from 'react';
 import { Search } from 'grommet-icons/icons/Search';
 import { Splits } from 'grommet-icons/icons/Splits';
+import { ThemeContext } from 'styled-components';
 import { Box } from '../Box';
 import { CheckBoxGroup } from '../CheckBoxGroup';
 import { DataForm, formColumnsKey } from '../Data';
@@ -149,6 +150,7 @@ export const DataTableColumns = ({ drop, options, ...rest }) => {
   const { id: dataId, messages } = useContext(DataContext);
   const { noForm } = useContext(FormContext);
   const { format } = useContext(MessageContext);
+  const theme = useContext(ThemeContext);
   const [showContent, setShowContent] = useState();
 
   const tip = format({
@@ -173,7 +175,7 @@ export const DataTableColumns = ({ drop, options, ...rest }) => {
         id: 'dataTableColumns.open',
         messages: messages?.dataTableColumns,
       })}
-      kind="toolbar"
+      kind={theme.data.button?.kind}
       icon={<Splits />}
       tip={tip}
       dropProps={dropProps}
diff --git a/src/js/components/DataTableColumns/__tests__/DataTableColumns-test.tsx b/src/js/components/DataTableColumns/__tests__/DataTableColumns-test.tsx
index 2dfb3f74aff..91a18a8aa94 100644
--- a/src/js/components/DataTableColumns/__tests__/DataTableColumns-test.tsx
+++ b/src/js/components/DataTableColumns/__tests__/DataTableColumns-test.tsx
@@ -45,8 +45,8 @@ describe('DataTableColumns', () => {
     const onView = jest.fn();
     const { container, getByRole, getByText } = render(
       <Grommet>
-        <Data id="test-data" data={data} updateOn="change" onView={onView}>
-          <DataFilters>
+        <Data id="test-data" data={data} onView={onView}>
+          <DataFilters updateOn="change">
             <DataTableColumns drop options={['name', 'size', 'age']} />
           </DataFilters>
           <DataTable
@@ -87,8 +87,8 @@ describe('DataTableColumns', () => {
     const onView = jest.fn();
     const { container, getByPlaceholderText, getByRole, getByText } = render(
       <Grommet>
-        <Data id="test-data" data={data} updateOn="change" onView={onView}>
-          <DataFilters>
+        <Data id="test-data" data={data} onView={onView}>
+          <DataFilters updateOn="change">
             <DataTableColumns drop options={['name', 'size']} />
           </DataFilters>
           <DataTable
diff --git a/src/js/components/DataTableColumns/stories/Simple.js b/src/js/components/DataTableColumns/stories/Simple.js
index f4b26b7b9f7..5e1a604926d 100644
--- a/src/js/components/DataTableColumns/stories/Simple.js
+++ b/src/js/components/DataTableColumns/stories/Simple.js
@@ -27,7 +27,7 @@ export const Simple = () => (
       status="info"
       message="Data is in 'beta'. The API surface is subject to change."
     />
-    <Data data={DATA} updateOn="change">
+    <Data data={DATA}>
       <Toolbar>
         <DataSearch />
         <DataTableColumns drop options={options} />
diff --git a/src/js/components/__tests__/__snapshots__/components-test.js.snap b/src/js/components/__tests__/__snapshots__/components-test.js.snap
index ddb0238a35b..41b566692b1 100644
--- a/src/js/components/__tests__/__snapshots__/components-test.js.snap
+++ b/src/js/components/__tests__/__snapshots__/components-test.js.snap
@@ -286,6 +286,43 @@ exports[`Components loads 1`] = `
     },
     "render": [Function],
   },
+  "DataClearFilters": {
+    "$$typeof": Symbol(react.forward_ref),
+    "propTypes": {
+      "a11yTitle": [Function],
+      "active": [Function],
+      "alignSelf": [Function],
+      "as": [Function],
+      "badge": [Function],
+      "busy": [Function],
+      "children": [Function],
+      "color": [Function],
+      "disabled": [Function],
+      "fill": [Function],
+      "focusIndicator": [Function],
+      "gap": [Function],
+      "gridArea": [Function],
+      "hoverIndicator": [Function],
+      "href": [Function],
+      "icon": [Function],
+      "justify": [Function],
+      "label": [Function],
+      "margin": [Function],
+      "messages": [Function],
+      "onClick": [Function],
+      "pad": [Function],
+      "plain": [Function],
+      "primary": [Function],
+      "reverse": [Function],
+      "secondary": [Function],
+      "size": [Function],
+      "success": [Function],
+      "target": [Function],
+      "tip": [Function],
+      "type": [Function],
+    },
+    "render": [Function],
+  },
   "DataFilter": [Function],
   "DataFilters": [Function],
   "DataForm": [Function],
@@ -1130,6 +1167,7 @@ exports[`Components loads 1`] = `
           "baseline": 500,
           "minSpeed": 200,
         },
+        "data": {},
         "dataTable": {
           "container": {
             "gap": "xsmall",
@@ -3131,6 +3169,7 @@ exports[`Components loads 1`] = `
           "baseline": 500,
           "minSpeed": 200,
         },
+        "data": {},
         "dataTable": {
           "container": {
             "gap": "xsmall",
diff --git a/src/js/components/index.d.ts b/src/js/components/index.d.ts
index 4c418c042a4..9158f0443ee 100644
--- a/src/js/components/index.d.ts
+++ b/src/js/components/index.d.ts
@@ -11,6 +11,7 @@ export * from './CardFooter';
 export * from './CardHeader';
 export * from './Cards';
 export * from './Carousel';
+export * from './DataClearFilters';
 export * from './CheckBoxGroup';
 export * from './Chart';
 export * from './CheckBox';
diff --git a/src/js/components/index.js b/src/js/components/index.js
index 2b8497b53eb..206488da150 100644
--- a/src/js/components/index.js
+++ b/src/js/components/index.js
@@ -18,6 +18,7 @@ export * from './Clock';
 export * from './Collapsible';
 export * from './Data';
 export * from './DataChart';
+export * from './DataClearFilters';
 export * from './DataFilter';
 export * from './DataFilters';
 export * from './DataSearch';
diff --git a/src/js/themes/base.d.ts b/src/js/themes/base.d.ts
index 509bf41fa4b..31d4aa6bcac 100644
--- a/src/js/themes/base.d.ts
+++ b/src/js/themes/base.d.ts
@@ -700,6 +700,11 @@ export interface ThemeType {
     minSpeed?: number;
     baseline?: number;
   };
+  data?: {
+    button?: {
+      kind?: string;
+    };
+  };
   dateInput?: {
     container?: {
       round?: RoundType;
diff --git a/src/js/themes/base.js b/src/js/themes/base.js
index 6c8710f93b5..2f0ff18f06e 100644
--- a/src/js/themes/base.js
+++ b/src/js/themes/base.js
@@ -781,6 +781,11 @@ export const generate = (baseSpacing = 24, scale = 6) => {
       minSpeed: 200,
       baseline: 500,
     },
+    data: {
+      // button: {
+      //   kind: undefined,
+      // },
+    },
     dateInput: {
       container: {
         round: 'xxsmall',
