diff --git a/src/js/components/Chart/calcs.js b/src/js/components/Chart/calcs.js
index 45495d4f654..b9846209ee6 100644
--- a/src/js/components/Chart/calcs.js
+++ b/src/js/components/Chart/calcs.js
@@ -30,8 +30,10 @@ export const calcBounds = (values, options = {}) => {
   const coarseness = (Array.isArray(options.coarseness) &&
     options.coarseness) ||
     (options.coarseness && [undefined, options.coarseness]) || [undefined, 5];
+  const [coarseX, coarseY] = coarseness;
   // the number of steps is one less than the number of labels
   const steps = options.steps || [1, 1];
+  const [, stepsY] = steps;
   const calcValues = normalizeValues(values || []);
 
   // min and max values
@@ -42,8 +44,8 @@ export const calcBounds = (values, options = {}) => {
   if (calcValues.length) {
     // Calculate the max and min values.
     calcValues
-      .filter(value => value !== undefined)
-      .forEach(value => {
+      .filter((value) => value !== undefined)
+      .forEach((value) => {
         const x = value.value[0];
         if (x !== undefined) {
           minX = minX === undefined ? x : Math.min(minX, x);
@@ -75,16 +77,15 @@ export const calcBounds = (values, options = {}) => {
     // Calculate some reasonable bounds based on the max and min values.
     // This is so values like 87342.12 don't end up being displayed as the
     // graph axis labels.
-    const [cX, cY] = coarseness;
-    if (cX) {
+    if (coarseX) {
       const deltaX = maxX - minX;
-      const intervalX = Number.parseFloat((deltaX / cX).toPrecision(1));
+      const intervalX = Number.parseFloat((deltaX / coarseX).toPrecision(1));
       minX = alignMin(minX, intervalX);
       maxX = alignMax(maxX, intervalX);
     }
-    if (cY) {
+    if (coarseY) {
       const deltaY = maxY - minY;
-      const intervalY = Number.parseFloat((deltaY / cY).toPrecision(1));
+      const intervalY = Number.parseFloat((deltaY / coarseY).toPrecision(1));
       minY = alignMin(minY, intervalY);
       maxY = alignMax(maxY, intervalY);
     }
@@ -92,19 +93,25 @@ export const calcBounds = (values, options = {}) => {
     if (minY < 0 && maxY > 0 && Math.abs(minY) !== Math.abs(maxY)) {
       // Adjust min and max when crossing 0 to ensure 0 will be shown on
       // the Y axis based on the number of steps.
-
-      // const ratio = Math.abs(max) / Math.abs(min);
-      let stepInterval = (maxY - minY) / steps[1];
-      const minSteps = minY / stepInterval;
-      const maxSteps = maxY / stepInterval;
-      if (Math.abs(minSteps) < Math.abs(maxSteps)) {
-        stepInterval = maxY / Math.floor(maxSteps);
-        maxY = stepInterval * Math.floor(maxSteps);
-        minY = stepInterval * Math.floor(minSteps);
+      if (stepsY === 1) {
+        const largest = Math.max(Math.abs(minY), Math.abs(maxY));
+        minY = -largest;
+        maxY = largest;
       } else {
-        stepInterval = Math.abs(minY / Math.ceil(minSteps));
-        minY = stepInterval * Math.ceil(minSteps);
-        maxY = stepInterval * Math.ceil(maxSteps);
+        let stepInterval = (maxY - minY) / stepsY;
+        const minSteps = minY / stepInterval;
+        const maxSteps = maxY / stepInterval;
+        if (Math.abs(minSteps) < Math.abs(maxSteps)) {
+          // more above than below
+          stepInterval = maxY / Math.floor(maxSteps);
+          maxY = stepInterval * Math.floor(maxSteps);
+          minY = stepInterval * Math.floor(minSteps);
+        } else {
+          // more below than above
+          stepInterval = Math.abs(minY / Math.ceil(minSteps));
+          minY = stepInterval * Math.ceil(minSteps);
+          maxY = stepInterval * Math.ceil(maxSteps);
+        }
       }
     }
   }
@@ -122,30 +129,35 @@ export const calcBounds = (values, options = {}) => {
 export const calcs = (values = [], options = {}) => {
   // the number of steps is one less than the number of labels
   const steps = options.steps || [1, 1];
+  const [stepsX, stepsY] = steps;
   const bounds = options.bounds || calcBounds(values, options);
   if (options.min !== undefined) bounds[1][0] = options.min;
   if (options.max !== undefined) bounds[1][1] = options.max;
+  const [boundsX, boundsY] = bounds;
+  const [boundsXmin, boundsXmax] = boundsX;
+  const [boundsYmin, boundsYmax] = boundsY;
 
   const dimensions = [
-    round(bounds[0][1] - bounds[0][0], 2),
-    round(bounds[1][1] - bounds[1][0], 2),
+    round(boundsXmax - boundsXmin, 2),
+    round(boundsYmax - boundsYmin, 2),
   ];
+  const [dimensionsX, dimensionsY] = dimensions;
 
   // Calculate x and y axis values across the specfied number of steps.
   const yAxis = [];
-  let y = bounds[1][1];
+  let y = boundsYmax;
   // To deal with javascript math limitations, round the step with 4 decimal
   // places and then push the values with 2 decimal places
-  const yStepInterval = round(dimensions[1] / steps[1], 4);
-  while (round(y, 2) >= bounds[1][0]) {
+  const yStepInterval = round(dimensionsY / stepsY, 4);
+  while (round(y, 2) >= boundsYmin) {
     yAxis.push(round(y, 2));
     y -= yStepInterval;
   }
 
   const xAxis = [];
-  let x = bounds[0][0];
-  const xStepInterval = round(dimensions[0] / steps[0], 4);
-  while (round(x, 2) <= bounds[0][1]) {
+  let x = boundsXmin;
+  const xStepInterval = round(dimensionsX / stepsX, 4);
+  while (round(x, 2) <= boundsXmax) {
     xAxis.push(round(x, 2));
     x += xStepInterval;
   }
diff --git a/src/js/components/Chart/stories/Scan.js b/src/js/components/Chart/stories/Scan.js
index dc1661564ce..d61b736500a 100644
--- a/src/js/components/Chart/stories/Scan.js
+++ b/src/js/components/Chart/stories/Scan.js
@@ -5,15 +5,17 @@ import { Box, Chart, Keyboard, Stack, Text } from 'grommet';
 import { calcs } from '../calcs';
 import { generateData } from './data';
 
-const ScanChart = (props) => {
+const [count, max, min] = [30, 100, 0];
+
+const ScanChart = () => {
   const [active, setActive] = useState(undefined);
 
-  const { data, max } = props;
+  const data = useMemo(() => generateData(count, max, min), []);
 
   const values = data.map((d) => [d.time, d.value]);
   const { axis, bounds, pad, thickness } = useMemo(
-    () => calcs(values, { min: 0, max }),
-    [values, max],
+    () => calcs(values, { min, max }),
+    [values],
   );
 
   return (
@@ -121,7 +123,7 @@ const ScanChart = (props) => {
   );
 };
 
-export const Scan = () => <ScanChart data={generateData(30, 100)} max={100} />;
+export const Scan = () => <ScanChart />;
 
 Scan.parameters = {
   chromatic: { disable: true },
diff --git a/src/js/components/Chart/stories/data.js b/src/js/components/Chart/stories/data.js
index ce1c91d480c..beea46cd401 100644
--- a/src/js/components/Chart/stories/data.js
+++ b/src/js/components/Chart/stories/data.js
@@ -1,22 +1,12 @@
 // generate a data set
 const dataSet = {
-  generateData: (count, max) => {
+  generateData: (count = 5, max = 100, min = 0) => {
     const data = [];
-    let value = max / 2;
-    let increase = true;
     let date = new Date();
+    const span = max - min;
     while (data.length < count) {
-      if (increase) {
-        value += (max - value) % Math.ceil(((data.length % (max / 4)) + 1) / 2);
-        if (value >= max) {
-          increase = false;
-        }
-      } else {
-        value -= value % Math.ceil(((data.length % (max / 4)) + 1) / 2);
-        if (value <= 0) {
-          increase = true;
-        }
-      }
+      const s = Math.sin(data.length / 2.0);
+      const value = s * (span / 2) + (span / 2 + min);
       const nextDay = date.getDate() - 1;
       date = new Date(date);
       date.setDate(nextDay);
diff --git a/src/js/components/DataChart/__tests__/DataChart-test.js b/src/js/components/DataChart/__tests__/DataChart-test.js
index b35d0676d63..81d05afc306 100644
--- a/src/js/components/DataChart/__tests__/DataChart-test.js
+++ b/src/js/components/DataChart/__tests__/DataChart-test.js
@@ -402,6 +402,22 @@ describe('DataChart', () => {
     expect(container.firstChild).toMatchSnapshot();
   });
 
+  test('negative values', () => {
+    const { container } = render(
+      <Grommet>
+        {[undefined, 'coarse', 'medium', 'find'].map((granularity) => (
+          <DataChart
+            key={granularity || 'u'}
+            data={[{ a: 1 }, { a: -2 }, { a: 3 }]}
+            series={['a']}
+          />
+        ))}
+      </Grommet>,
+    );
+
+    expect(container.firstChild).toMatchSnapshot();
+  });
+
   test('placeholder text', () => {
     const { container } = render(
       <Grommet>
diff --git a/src/js/components/DataChart/__tests__/__snapshots__/DataChart-test.js.snap b/src/js/components/DataChart/__tests__/__snapshots__/DataChart-test.js.snap
index c3bf6d3cb9e..c7a81a5a9f6 100644
--- a/src/js/components/DataChart/__tests__/__snapshots__/DataChart-test.js.snap
+++ b/src/js/components/DataChart/__tests__/__snapshots__/DataChart-test.js.snap
@@ -7556,6 +7556,457 @@ exports[`DataChart lines 1`] = `
 </div>
 `;
 
+exports[`DataChart negative values 1`] = `
+.c0 {
+  font-size: 18px;
+  line-height: 24px;
+  box-sizing: border-box;
+  -webkit-text-size-adjust: 100%;
+  -ms-text-size-adjust: 100%;
+  -moz-osx-font-smoothing: grayscale;
+  -webkit-font-smoothing: antialiased;
+}
+
+.c2 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  grid-area: xAxis;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: row;
+  -ms-flex-direction: row;
+  flex-direction: row;
+  -webkit-box-pack: justify;
+  -webkit-justify-content: space-between;
+  -ms-flex-pack: justify;
+  justify-content: space-between;
+  padding-left: 48px;
+  padding-right: 48px;
+}
+
+.c3 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: column;
+  -ms-flex-direction: column;
+  flex-direction: column;
+}
+
+.c4 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  grid-area: yAxis;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: column;
+  -ms-flex-direction: column;
+  flex-direction: column;
+  -webkit-flex: 1 1;
+  -ms-flex: 1 1;
+  flex: 1 1;
+  -webkit-box-pack: justify;
+  -webkit-justify-content: space-between;
+  -ms-flex-pack: justify;
+  justify-content: space-between;
+}
+
+.c5 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  -webkit-align-items: flex-end;
+  -webkit-box-align: flex-end;
+  -ms-flex-align: flex-end;
+  align-items: flex-end;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: column;
+  -ms-flex-direction: column;
+  flex-direction: column;
+  -webkit-flex: 0 1 auto;
+  -ms-flex: 0 1 auto;
+  flex: 0 1 auto;
+}
+
+.c8 {
+  display: block;
+  max-width: 100%;
+  overflow: visible;
+}
+
+.c1 {
+  display: grid;
+  box-sizing: border-box;
+  grid-template-areas: "yAxis charts" ". xAxis";
+  grid-template-columns: auto auto;
+  grid-gap: 12px 12px;
+  grid-template-rows: auto auto;
+}
+
+.c6 {
+  position: relative;
+  grid-area: charts;
+}
+
+.c7 {
+  position: relative;
+  display: block;
+}
+
+@media only screen and (max-width:768px) {
+  .c2 {
+    padding-left: 24px;
+    padding-right: 24px;
+  }
+}
+
+<div
+  class="c0"
+>
+  <div
+    class="c1"
+  >
+    <div
+      class="c2"
+    >
+      <div
+        class="c3"
+      >
+        0
+      </div>
+      <div
+        class="c3"
+      >
+        2
+      </div>
+    </div>
+    <div
+      class="c4"
+    >
+      <div
+        class="c5"
+      >
+        4
+      </div>
+      <div
+        class="c5"
+      >
+        -4
+      </div>
+    </div>
+    <div
+      class="c6"
+    >
+      <div
+        class="c7"
+      >
+        <svg
+          class="c8"
+          height="192"
+          preserveAspectRatio="none"
+          viewBox="0 0 384 192"
+          width="384"
+        >
+          0
+          <g
+            fill="none"
+            stroke="#6FFFB0"
+            stroke-linecap="butt"
+            stroke-linejoin="miter"
+            stroke-width="96"
+          >
+            <g
+              fill="none"
+            >
+              <title />
+              <path
+                d="M 48,96 L 48,72"
+              />
+            </g>
+            <g
+              fill="none"
+            >
+              <title />
+              <path
+                d="M 192,144 L 192,96"
+              />
+            </g>
+            <g
+              fill="none"
+            >
+              <title />
+              <path
+                d="M 336,96 L 336,24"
+              />
+            </g>
+          </g>
+        </svg>
+      </div>
+    </div>
+  </div>
+  <div
+    class="c1"
+  >
+    <div
+      class="c2"
+    >
+      <div
+        class="c3"
+      >
+        0
+      </div>
+      <div
+        class="c3"
+      >
+        2
+      </div>
+    </div>
+    <div
+      class="c4"
+    >
+      <div
+        class="c5"
+      >
+        4
+      </div>
+      <div
+        class="c5"
+      >
+        -4
+      </div>
+    </div>
+    <div
+      class="c6"
+    >
+      <div
+        class="c7"
+      >
+        <svg
+          class="c8"
+          height="192"
+          preserveAspectRatio="none"
+          viewBox="0 0 384 192"
+          width="384"
+        >
+          0
+          <g
+            fill="none"
+            stroke="#6FFFB0"
+            stroke-linecap="butt"
+            stroke-linejoin="miter"
+            stroke-width="96"
+          >
+            <g
+              fill="none"
+            >
+              <title />
+              <path
+                d="M 48,96 L 48,72"
+              />
+            </g>
+            <g
+              fill="none"
+            >
+              <title />
+              <path
+                d="M 192,144 L 192,96"
+              />
+            </g>
+            <g
+              fill="none"
+            >
+              <title />
+              <path
+                d="M 336,96 L 336,24"
+              />
+            </g>
+          </g>
+        </svg>
+      </div>
+    </div>
+  </div>
+  <div
+    class="c1"
+  >
+    <div
+      class="c2"
+    >
+      <div
+        class="c3"
+      >
+        0
+      </div>
+      <div
+        class="c3"
+      >
+        2
+      </div>
+    </div>
+    <div
+      class="c4"
+    >
+      <div
+        class="c5"
+      >
+        4
+      </div>
+      <div
+        class="c5"
+      >
+        -4
+      </div>
+    </div>
+    <div
+      class="c6"
+    >
+      <div
+        class="c7"
+      >
+        <svg
+          class="c8"
+          height="192"
+          preserveAspectRatio="none"
+          viewBox="0 0 384 192"
+          width="384"
+        >
+          0
+          <g
+            fill="none"
+            stroke="#6FFFB0"
+            stroke-linecap="butt"
+            stroke-linejoin="miter"
+            stroke-width="96"
+          >
+            <g
+              fill="none"
+            >
+              <title />
+              <path
+                d="M 48,96 L 48,72"
+              />
+            </g>
+            <g
+              fill="none"
+            >
+              <title />
+              <path
+                d="M 192,144 L 192,96"
+              />
+            </g>
+            <g
+              fill="none"
+            >
+              <title />
+              <path
+                d="M 336,96 L 336,24"
+              />
+            </g>
+          </g>
+        </svg>
+      </div>
+    </div>
+  </div>
+  <div
+    class="c1"
+  >
+    <div
+      class="c2"
+    >
+      <div
+        class="c3"
+      >
+        0
+      </div>
+      <div
+        class="c3"
+      >
+        2
+      </div>
+    </div>
+    <div
+      class="c4"
+    >
+      <div
+        class="c5"
+      >
+        4
+      </div>
+      <div
+        class="c5"
+      >
+        -4
+      </div>
+    </div>
+    <div
+      class="c6"
+    >
+      <div
+        class="c7"
+      >
+        <svg
+          class="c8"
+          height="192"
+          preserveAspectRatio="none"
+          viewBox="0 0 384 192"
+          width="384"
+        >
+          0
+          <g
+            fill="none"
+            stroke="#6FFFB0"
+            stroke-linecap="butt"
+            stroke-linejoin="miter"
+            stroke-width="96"
+          >
+            <g
+              fill="none"
+            >
+              <title />
+              <path
+                d="M 48,96 L 48,72"
+              />
+            </g>
+            <g
+              fill="none"
+            >
+              <title />
+              <path
+                d="M 192,144 L 192,96"
+              />
+            </g>
+            <g
+              fill="none"
+            >
+              <title />
+              <path
+                d="M 336,96 L 336,24"
+              />
+            </g>
+          </g>
+        </svg>
+      </div>
+    </div>
+  </div>
+</div>
+`;
+
 exports[`DataChart nothing 1`] = `
 .c0 {
   font-size: 18px;
