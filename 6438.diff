diff --git a/src/js/components/DateInput/DateInput.js b/src/js/components/DateInput/DateInput.js
index 057eebcaadf..4d7d607947b 100644
--- a/src/js/components/DateInput/DateInput.js
+++ b/src/js/components/DateInput/DateInput.js
@@ -27,6 +27,7 @@ import {
   valuesAreEqual,
   valueToText,
   textToValue,
+  validateBounds,
 } from './utils';
 import { DateInputPropTypes } from './propTypes';
 import { getOutputFormat } from '../Calendar/Calendar';
@@ -274,14 +275,24 @@ Use the icon prop instead.`,
                   reference,
                   outputFormat,
                 );
-                if (nextValue !== undefined)
-                  setReference(getReference(nextValue));
+
+                const validatedNextValue = validateBounds(
+                  calendarProps?.bounds,
+                  nextValue,
+                );
+
+                if (!validatedNextValue && nextValue) {
+                  setTextValue('');
+                }
+
+                if (validatedNextValue !== undefined)
+                  setReference(getReference(validatedNextValue));
                 // update value even when undefined
-                setValue(nextValue);
+                setValue(validatedNextValue);
                 if (onChange) {
                   event.persist(); // extract from React synthetic event pool
                   const adjustedEvent = event;
-                  adjustedEvent.value = nextValue;
+                  adjustedEvent.value = validatedNextValue;
                   onChange(adjustedEvent);
                 }
               }}
diff --git a/src/js/components/DateInput/__tests__/DateInput-test.tsx b/src/js/components/DateInput/__tests__/DateInput-test.tsx
index 868d0a6a2f1..3d0c6a201b6 100644
--- a/src/js/components/DateInput/__tests__/DateInput-test.tsx
+++ b/src/js/components/DateInput/__tests__/DateInput-test.tsx
@@ -106,6 +106,32 @@ describe('DateInput', () => {
     expect(container.firstChild).toMatchSnapshot();
   });
 
+  test('format with date bounds', async () => {
+    const user = userEvent.setup();
+
+    render(
+      <Grommet>
+        <DateInput
+          id="item"
+          name="item"
+          format="mm/dd/yyyy"
+          calendarProps={{
+            bounds: ['2022-11-10', '2022-11-20'],
+          }}
+        />
+      </Grommet>,
+    );
+
+    const input = screen.getByRole('textbox');
+
+    await user.type(input, '09/09/2022');
+    expect(input).not.toHaveValue();
+
+    await user.clear(input);
+    await user.type(input, '11/15/2022');
+    expect(input).toHaveValue('11/15/2022');
+  });
+
   test('reverse calendar icon', () => {
     const { container } = render(
       <Grommet>
@@ -279,6 +305,32 @@ describe('DateInput', () => {
     expect(container.firstChild).toMatchSnapshot();
   });
 
+  test('range format with date bounds', async () => {
+    const user = userEvent.setup();
+
+    render(
+      <Grommet>
+        <DateInput
+          id="item"
+          name="item"
+          format="mm/dd/yyyy-mm/dd/yyyy"
+          calendarProps={{
+            bounds: ['2022-11-10', '2022-11-20'],
+          }}
+        />
+      </Grommet>,
+    );
+
+    const input = screen.getByRole('textbox');
+
+    await user.type(input, '09/09/2022-09/09/2022');
+    expect(input).not.toHaveValue();
+
+    await user.clear(input);
+    await user.type(input, '11/15/2022-11/15/2022');
+    expect(input).toHaveValue('11/15/2022-11/15/2022');
+  });
+
   test('dates initialized with empty array', async () => {
     const user = userEvent.setup();
 
diff --git a/src/js/components/DateInput/utils.js b/src/js/components/DateInput/utils.js
index c9d90ebad58..83ff8db154b 100644
--- a/src/js/components/DateInput/utils.js
+++ b/src/js/components/DateInput/utils.js
@@ -118,6 +118,26 @@ const pullDigits = (text, index) => {
   return text.slice(index, end);
 };
 
+export const validateBounds = (dateBounds, selectedDate) => {
+  if (!dateBounds || !selectedDate) return selectedDate;
+
+  const [startDate, endDate] = dateBounds.map((date) =>
+    setHoursWithOffset(date).toISOString(),
+  );
+
+  const isoSelectedDates = (
+    Array.isArray(selectedDate) ? selectedDate : [selectedDate]
+  ).map((date) => setHoursWithOffset(date).toISOString());
+
+  const validSelection = isoSelectedDates.every(
+    (isoSelectedDate) =>
+      (!endDate && startDate === isoSelectedDate) ||
+      (isoSelectedDate >= startDate && isoSelectedDate <= endDate),
+  );
+
+  return validSelection ? selectedDate : undefined;
+};
+
 export const textToValue = (text, schema, range, reference, outputFormat) => {
   if (!text) return range ? [] : undefined;
 
