diff --git a/src/js/components/Grommet/__tests__/__snapshots__/Grommet-test.js.snap b/src/js/components/Grommet/__tests__/__snapshots__/Grommet-test.js.snap
index cbf00bd2da2..66d7a0f0d1d 100644
--- a/src/js/components/Grommet/__tests__/__snapshots__/Grommet-test.js.snap
+++ b/src/js/components/Grommet/__tests__/__snapshots__/Grommet-test.js.snap
@@ -123,7 +123,7 @@ exports[`Grommet cssVars 1`] = `
   --neutral-3: #00739D;
   --neutral-4: #A2423D;
   --status-critical: #FF4040;
-  --status-error: #FF4040;
+  --status-error: #B30000;
   --status-warning: #FFAA15;
   --status-ok: #00C781;
   --status-unknown: #CCCCCC;
diff --git a/src/js/components/__tests__/__snapshots__/components-test.js.snap b/src/js/components/__tests__/__snapshots__/components-test.js.snap
index 7b7e0ae8143..32c25901d07 100644
--- a/src/js/components/__tests__/__snapshots__/components-test.js.snap
+++ b/src/js/components/__tests__/__snapshots__/components-test.js.snap
@@ -1422,7 +1422,7 @@ exports[`Components loads 1`] = `
             "selected-text": "text-strong",
             "status-critical": "#FF4040",
             "status-disabled": "#CCCCCC",
-            "status-error": "#FF4040",
+            "status-error": "#B30000",
             "status-ok": "#00C781",
             "status-unknown": "#CCCCCC",
             "status-warning": "#FFAA15",
@@ -3417,7 +3417,7 @@ exports[`Components loads 1`] = `
             "selected-text": "text-strong",
             "status-critical": "#FF4040",
             "status-disabled": "#CCCCCC",
-            "status-error": "#FF4040",
+            "status-error": "#B30000",
             "status-ok": "#00C781",
             "status-unknown": "#CCCCCC",
             "status-warning": "#FFAA15",
diff --git a/src/js/themes/base.js b/src/js/themes/base.js
index 28d4ef45fec..45589f42442 100644
--- a/src/js/themes/base.js
+++ b/src/js/themes/base.js
@@ -30,7 +30,7 @@ const accentColors = ['#6FFFB0', '#FD6FFF', '#81FCED', '#FFCA58'];
 const neutralColors = ['#00873D', '#3D138D', '#00739D', '#A2423D'];
 const statusColors = {
   critical: '#FF4040',
-  error: '#FF4040',
+  error: '#B30000',
   warning: '#FFAA15',
   ok: '#00C781',
   unknown: '#CCCCCC',
