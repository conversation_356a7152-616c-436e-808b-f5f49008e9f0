diff --git a/src/js/components/NameValueList/NameValueList.js b/src/js/components/NameValueList/NameValueList.js
index bda605be84c..efcea2318d9 100644
--- a/src/js/components/NameValueList/NameValueList.js
+++ b/src/js/components/NameValueList/NameValueList.js
@@ -55,6 +55,7 @@ const NameValueList = forwardRef(
         <Grid
           as="dl"
           ref={ref}
+          align={align}
           columns={columns}
           gap={gap}
           margin="none" // override browser default margin for dl
diff --git a/src/js/components/NameValueList/__tests__/NameValueList-test.tsx b/src/js/components/NameValueList/__tests__/NameValueList-test.tsx
index 7e09c0cf2d5..3d707c14580 100644
--- a/src/js/components/NameValueList/__tests__/NameValueList-test.tsx
+++ b/src/js/components/NameValueList/__tests__/NameValueList-test.tsx
@@ -147,6 +147,26 @@ describe('NameValueList', () => {
     expect(container.firstChild).toMatchSnapshot();
   });
 
+  test(`should accept and apply Grid props`, () => {
+    const { container } = render(
+      <Grommet>
+        <NameValueList
+          align="center"
+          justify="center"
+          gap={{ row: 'large', column: 'xlarge' }}
+        >
+          {Object.entries(data).map(([name, value]) => (
+            <NameValuePair key={name} name={name}>
+              {value}
+            </NameValuePair>
+          ))}
+        </NameValueList>
+      </Grommet>,
+    );
+
+    expect(container.firstChild).toMatchSnapshot();
+  });
+
   test(`should render name/value as a column when pairProps = { direction: 
     'column' }`, () => {
     const { container } = render(
diff --git a/src/js/components/NameValueList/__tests__/__snapshots__/NameValueList-test.tsx.snap b/src/js/components/NameValueList/__tests__/__snapshots__/NameValueList-test.tsx.snap
index 1e9e6107546..feb86259fe0 100644
--- a/src/js/components/NameValueList/__tests__/__snapshots__/NameValueList-test.tsx.snap
+++ b/src/js/components/NameValueList/__tests__/__snapshots__/NameValueList-test.tsx.snap
@@ -1,5 +1,120 @@
 // Jest Snapshot v1, https://goo.gl/fbAQLP
 
+exports[`NameValueList should accept and apply Grid props 1`] = `
+.c0 {
+  font-size: 18px;
+  line-height: 24px;
+  box-sizing: border-box;
+  -webkit-text-size-adjust: 100%;
+  -ms-text-size-adjust: 100%;
+  -moz-osx-font-smoothing: grayscale;
+  -webkit-font-smoothing: antialiased;
+}
+
+.c1 {
+  display: grid;
+  box-sizing: border-box;
+  margin: 0px;
+  -webkit-align-items: center;
+  -webkit-box-align: center;
+  -ms-flex-align: center;
+  align-items: center;
+  grid-template-columns: repeat( auto-fit,minmax(auto,384px) );
+  grid-gap: 48px 96px;
+  justify-items: center;
+}
+
+.c2 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: column;
+  -ms-flex-direction: column;
+  flex-direction: column;
+}
+
+.c4 {
+  margin: 0px;
+  font-size: 18px;
+  line-height: 24px;
+  color: #444444;
+}
+
+.c3 {
+  margin-bottom: 3px;
+  font-size: 18px;
+  line-height: 24px;
+  color: #444444;
+  font-weight: bold;
+}
+
+@media only screen and (max-width:768px) {
+
+}
+
+@media only screen and (max-width:768px) {
+  .c1 {
+    margin: 0px;
+  }
+}
+
+<div
+  class="c0"
+>
+  <dl
+    class="c1"
+  >
+    <div
+      class="c2"
+    >
+      <dt
+        class="c3"
+      >
+        name
+      </dt>
+      <dd
+        class="c4"
+      >
+        entry
+      </dd>
+    </div>
+    <div
+      class="c2"
+    >
+      <dt
+        class="c3"
+      >
+        location
+      </dt>
+      <dd
+        class="c4"
+      >
+        San Francisco
+      </dd>
+    </div>
+    <div
+      class="c2"
+    >
+      <dt
+        class="c3"
+      >
+        health
+      </dt>
+      <dd
+        class="c4"
+      >
+        80
+      </dd>
+    </div>
+  </dl>
+</div>
+`;
+
 exports[`NameValueList should render 1`] = `
 .c0 {
   font-size: 18px;
