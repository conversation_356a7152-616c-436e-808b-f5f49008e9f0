diff --git a/src/js/components/Meter/StyledMeter.js b/src/js/components/Meter/StyledMeter.js
index b082779b1da..b5bc56d5537 100644
--- a/src/js/components/Meter/StyledMeter.js
+++ b/src/js/components/Meter/StyledMeter.js
@@ -15,7 +15,7 @@ const StyledMeter = styled.svg`
   ${genericStyles} ${props => props.round && roundStyle}
 
   path {
-    transition: all 0.3s;
+    transition: stroke 0.3s, stroke-width 0.3s;
   }
 
   ${props => props.theme.meter && props.theme.meter.extend};
diff --git a/src/js/components/Meter/__tests__/__snapshots__/Meter-test.js.snap b/src/js/components/Meter/__tests__/__snapshots__/Meter-test.js.snap
index 1c094dd1ab9..979d048199c 100644
--- a/src/js/components/Meter/__tests__/__snapshots__/Meter-test.js.snap
+++ b/src/js/components/Meter/__tests__/__snapshots__/Meter-test.js.snap
@@ -17,8 +17,8 @@ exports[`Meter background 1`] = `
 }
 
 .c1 path {
-  -webkit-transition: all 0.3s;
-  transition: all 0.3s;
+  -webkit-transition: stroke 0.3s,stroke-width 0.3s;
+  transition: stroke 0.3s,stroke-width 0.3s;
 }
 
 <div
@@ -144,8 +144,8 @@ exports[`Meter basic 1`] = `
 }
 
 .c1 path {
-  -webkit-transition: all 0.3s;
-  transition: all 0.3s;
+  -webkit-transition: stroke 0.3s,stroke-width 0.3s;
+  transition: stroke 0.3s,stroke-width 0.3s;
 }
 
 <div
@@ -196,8 +196,8 @@ exports[`Meter default 1`] = `
 }
 
 .c1 path {
-  -webkit-transition: all 0.3s;
-  transition: all 0.3s;
+  -webkit-transition: stroke 0.3s,stroke-width 0.3s;
+  transition: stroke 0.3s,stroke-width 0.3s;
 }
 
 <div
@@ -239,8 +239,8 @@ exports[`Meter many values 1`] = `
 }
 
 .c1 path {
-  -webkit-transition: all 0.3s;
-  transition: all 0.3s;
+  -webkit-transition: stroke 0.3s,stroke-width 0.3s;
+  transition: stroke 0.3s,stroke-width 0.3s;
 }
 
 <div
@@ -338,8 +338,8 @@ exports[`Meter round 1`] = `
 }
 
 .c2 path {
-  -webkit-transition: all 0.3s;
-  transition: all 0.3s;
+  -webkit-transition: stroke 0.3s,stroke-width 0.3s;
+  transition: stroke 0.3s,stroke-width 0.3s;
 }
 
 .c1 {
@@ -349,8 +349,8 @@ exports[`Meter round 1`] = `
 }
 
 .c1 path {
-  -webkit-transition: all 0.3s;
-  transition: all 0.3s;
+  -webkit-transition: stroke 0.3s,stroke-width 0.3s;
+  transition: stroke 0.3s,stroke-width 0.3s;
 }
 
 <div
@@ -436,8 +436,8 @@ exports[`Meter size 1`] = `
 }
 
 .c1 path {
-  -webkit-transition: all 0.3s;
-  transition: all 0.3s;
+  -webkit-transition: stroke 0.3s,stroke-width 0.3s;
+  transition: stroke 0.3s,stroke-width 0.3s;
 }
 
 <div
@@ -718,8 +718,8 @@ exports[`Meter thickness 1`] = `
 }
 
 .c1 path {
-  -webkit-transition: all 0.3s;
-  transition: all 0.3s;
+  -webkit-transition: stroke 0.3s,stroke-width 0.3s;
+  transition: stroke 0.3s,stroke-width 0.3s;
 }
 
 <div
@@ -1051,8 +1051,8 @@ exports[`Meter type 1`] = `
 }
 
 .c1 path {
-  -webkit-transition: all 0.3s;
-  transition: all 0.3s;
+  -webkit-transition: stroke 0.3s,stroke-width 0.3s;
+  transition: stroke 0.3s,stroke-width 0.3s;
 }
 
 <div
diff --git a/src/js/components/Meter/meter.stories.js b/src/js/components/Meter/meter.stories.js
index e7d7966423f..8c99bb3beef 100644
--- a/src/js/components/Meter/meter.stories.js
+++ b/src/js/components/Meter/meter.stories.js
@@ -12,13 +12,35 @@ const BarMeter = () => (
   </Grommet>
 );
 
-const CircleMeter = () => (
-  <Grommet theme={grommet}>
-    <Box align="center" pad="large">
-      <Meter type="circle" background="light-2" values={[{ value: 30 }]} />
-    </Box>
-  </Grommet>
-);
+class CircleMeter extends React.Component {
+  state = { value: 20 };
+
+  componentDidMount() {
+    this.timer = setInterval(() => {
+      const { value } = this.state;
+      this.setState({ value: value < 100 ? value + 8 : 20 });
+    }, 2000);
+  }
+
+  componentWillUnmount() {
+    clearInterval(this.timer);
+  }
+
+  render() {
+    const { value } = this.state;
+    return (
+      <Grommet theme={grommet}>
+        <Box align="center" pad="large">
+          <Meter
+            type="circle"
+            background="light-2"
+            values={[{ value, color: value > 50 ? 'accent-2' : 'accent-1' }]}
+          />
+        </Box>
+      </Grommet>
+    );
+  }
+}
 
 const LabelledMeter = () => (
   <Grommet theme={grommet}>
diff --git a/src/js/components/Video/__tests__/__snapshots__/Video-test.js.snap b/src/js/components/Video/__tests__/__snapshots__/Video-test.js.snap
index c422afc4e1b..db5f7a02099 100644
--- a/src/js/components/Video/__tests__/__snapshots__/Video-test.js.snap
+++ b/src/js/components/Video/__tests__/__snapshots__/Video-test.js.snap
@@ -240,8 +240,8 @@ exports[`Video autoPlay renders 1`] = `
 }
 
 .c11 path {
-  -webkit-transition: all 0.3s;
-  transition: all 0.3s;
+  -webkit-transition: stroke 0.3s,stroke-width 0.3s;
+  transition: stroke 0.3s,stroke-width 0.3s;
 }
 
 .c2 {
@@ -824,8 +824,8 @@ exports[`Video controls renders 1`] = `
 }
 
 .c11 path {
-  -webkit-transition: all 0.3s;
-  transition: all 0.3s;
+  -webkit-transition: stroke 0.3s,stroke-width 0.3s;
+  transition: stroke 0.3s,stroke-width 0.3s;
 }
 
 .c2 {
@@ -1490,8 +1490,8 @@ exports[`Video fit renders 1`] = `
 }
 
 .c11 path {
-  -webkit-transition: all 0.3s;
-  transition: all 0.3s;
+  -webkit-transition: stroke 0.3s,stroke-width 0.3s;
+  transition: stroke 0.3s,stroke-width 0.3s;
 }
 
 .c2 {
@@ -2156,8 +2156,8 @@ exports[`Video loop renders 1`] = `
 }
 
 .c11 path {
-  -webkit-transition: all 0.3s;
-  transition: all 0.3s;
+  -webkit-transition: stroke 0.3s,stroke-width 0.3s;
+  transition: stroke 0.3s,stroke-width 0.3s;
 }
 
 .c2 {
@@ -2656,8 +2656,8 @@ exports[`Video mute renders 1`] = `
 }
 
 .c11 path {
-  -webkit-transition: all 0.3s;
-  transition: all 0.3s;
+  -webkit-transition: stroke 0.3s,stroke-width 0.3s;
+  transition: stroke 0.3s,stroke-width 0.3s;
 }
 
 .c2 {
@@ -3156,8 +3156,8 @@ exports[`Video renders 1`] = `
 }
 
 .c11 path {
-  -webkit-transition: all 0.3s;
-  transition: all 0.3s;
+  -webkit-transition: stroke 0.3s,stroke-width 0.3s;
+  transition: stroke 0.3s,stroke-width 0.3s;
 }
 
 .c2 {
