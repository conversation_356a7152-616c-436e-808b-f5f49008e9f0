diff --git a/src/js/components/Box/Box.js b/src/js/components/Box/Box.js
index 192fd386f7d..dbc0ca5143e 100644
--- a/src/js/components/Box/Box.js
+++ b/src/js/components/Box/Box.js
@@ -29,9 +29,17 @@ class Box extends Component {
 
     let dark = theme.dark;
     if (background) {
-      dark = false;
       if (typeof background === 'object') {
-        dark = background.dark;
+        if (background.dark !== undefined) {
+          dark = background.dark;
+        } else if (background.color &&
+          // weak opacity means we keep the existing darkness
+          (!background.opacity || background.opacity !== 'weak')) {
+          const color = colorForName(background.color, theme);
+          if (color) {
+            dark = colorIsDark(color);
+          }
+        }
       } else {
         const color = colorForName(background, theme);
         if (color) {
diff --git a/src/js/components/Box/box.stories.js b/src/js/components/Box/box.stories.js
index ad4cc321d49..3c364442454 100644
--- a/src/js/components/Box/box.stories.js
+++ b/src/js/components/Box/box.stories.js
@@ -19,13 +19,21 @@ class SimpleBox extends Component {
           pad='xlarge'
           background='dark-2'
         >
-          <Box pad='xlarge' align='center' background='white-2'>
+          <Box
+            pad='xlarge'
+            align='center'
+            background={{ color: 'white-2', opacity: 'strong' }}
+          >
             <Attraction size='xlarge' />
             <Text>Party</Text>
             <Anchor href='' label='Link' />
             <Button label='Button' onClick={() => {}} />
           </Box>
-          <Box pad='xlarge' align='center' background='accent-2'>
+          <Box
+            pad='xlarge'
+            align='center'
+            background={{ color: 'accent-2', opacity: 'weak' }}
+          >
             <TreeOption size='xlarge' />
             <Text>Nature</Text>
             <Anchor href='' label='Link' />
diff --git a/src/js/components/Calendar/Calendar.js b/src/js/components/Calendar/Calendar.js
index 88da295b744..8bf6e3b9ed1 100644
--- a/src/js/components/Calendar/Calendar.js
+++ b/src/js/components/Calendar/Calendar.js
@@ -6,7 +6,6 @@ import { Box } from '../Box';
 import { Button } from '../Button';
 import { Keyboard } from '../Keyboard';
 import { Heading } from '../Heading';
-import { Text } from '../Text';
 
 import { withTheme } from '../hocs';
 
@@ -165,11 +164,9 @@ class Calendar extends Component {
       }
 
       const dateString = day.toISOString();
-      let content = day.getDate();
       const isActive = active && sameDay(day, active);
       let selected = false;
       let inRange = false;
-      let background;
 
       const selectedState = withinDates(day, date || dates);
       if (selectedState === 2) {
@@ -178,12 +175,6 @@ class Calendar extends Component {
         inRange = true;
       }
       const dayDisabled = withinDates(day, disabled);
-      if (selected) {
-        background = 'brand';
-        content = <Text weight='bold'>{content}</Text>;
-      } else if (inRange) {
-        background = { color: 'brand', opacity: 'weak' };
-      }
 
       days.push(
         <StyledDayContainer key={day.getTime()} size={size} theme={theme}>
@@ -198,12 +189,13 @@ class Calendar extends Component {
             onClick={dayDisabled ? undefined : this.onClickDay(dateString)}
           >
             <StyledDay
-              background={background}
+              inRange={inRange}
               otherMonth={day.getMonth() !== reference.getMonth()}
+              isSelected={selected}
               size={size}
               theme={theme}
             >
-              {content}
+              {day.getDate()}
             </StyledDay>
           </Button>
         </StyledDayContainer>
diff --git a/src/js/components/Calendar/StyledCalendar.js b/src/js/components/Calendar/StyledCalendar.js
index e1a4eeb7c48..840911beec1 100644
--- a/src/js/components/Calendar/StyledCalendar.js
+++ b/src/js/components/Calendar/StyledCalendar.js
@@ -43,7 +43,7 @@ export const StyledWeek = styled.div`
 `;
 
 export const StyledDayContainer = styled.div`
-  flex: 0 0;
+  flex: 0 0 auto;
 `;
 
 const daySizeStyle = (props) => {
@@ -59,8 +59,11 @@ export const StyledDay = styled.div`
   justify-content: center;
   align-items: center;
   ${props => daySizeStyle(props)}
-  ${props => props.background && backgroundStyle(props.background, props.theme)}
+  ${props => (props.isSelected && backgroundStyle('brand', props.theme)) ||
+    (props.inRange &&
+      backgroundStyle({ color: 'brand', opacity: 'weak' }, props.theme))}
   ${props => props.otherMonth && 'opacity: 0.5;'}
+  ${props => props.isSelected && 'font-weight: bold;'}
 `;
 
 export default StyledCalendar.extend`
diff --git a/src/js/components/Calendar/__tests__/__snapshots__/Calendar-test.js.snap b/src/js/components/Calendar/__tests__/__snapshots__/Calendar-test.js.snap
index c0080cbe411..9cdffcb4a3c 100644
--- a/src/js/components/Calendar/__tests__/__snapshots__/Calendar-test.js.snap
+++ b/src/js/components/Calendar/__tests__/__snapshots__/Calendar-test.js.snap
@@ -133,12 +133,6 @@ exports[`Calendar date renders 1`] = `
   padding: 12px;
 }
 
-.c18 {
-  font-size: 16px;
-  line-height: 1.375;
-  font-weight: bold;
-}
-
 .c6 {
   box-sizing: border-box;
   cursor: pointer;
@@ -227,9 +221,9 @@ exports[`Calendar date renders 1`] = `
 }
 
 .c12 {
-  -webkit-flex: 0 0;
-  -ms-flex: 0 0;
-  flex: 0 0;
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
 }
 
 .c14 {
@@ -284,6 +278,7 @@ exports[`Calendar date renders 1`] = `
   height: 54.857142857142854px;
   background-color: #865CD6;
   color: #FFFFFF;
+  font-weight: bold;
 }
 
 .c1 {
@@ -834,11 +829,7 @@ exports[`Calendar date renders 1`] = `
                   className="c17"
                   size="medium"
                 >
-                  <span
-                    className="c18"
-                  >
-                    15
-                  </span>
+                  15
                 </div>
               </button>
             </div>
@@ -1520,12 +1511,6 @@ exports[`Calendar dates renders 1`] = `
   padding: 12px;
 }
 
-.c17 {
-  font-size: 16px;
-  line-height: 1.375;
-  font-weight: bold;
-}
-
 .c6 {
   box-sizing: border-box;
   cursor: pointer;
@@ -1567,7 +1552,7 @@ exports[`Calendar dates renders 1`] = `
   color: #000000;
 }
 
-.c18 {
+.c17 {
   box-sizing: border-box;
   cursor: pointer;
   outline: none;
@@ -1586,7 +1571,7 @@ exports[`Calendar dates renders 1`] = `
   color: #000000;
 }
 
-.c18:hover {
+.c17:hover {
   background-color: rgba(221,221,221,0.5);
   color: #000000;
 }
@@ -1614,9 +1599,9 @@ exports[`Calendar dates renders 1`] = `
 }
 
 .c12 {
-  -webkit-flex: 0 0;
-  -ms-flex: 0 0;
-  flex: 0 0;
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
 }
 
 .c14 {
@@ -1671,6 +1656,7 @@ exports[`Calendar dates renders 1`] = `
   height: 54.857142857142854px;
   background-color: #865CD6;
   color: #FFFFFF;
+  font-weight: bold;
 }
 
 .c1 {
@@ -1752,7 +1738,7 @@ exports[`Calendar dates renders 1`] = `
 }
 
 @media only screen and (min-width:700px) {
-  .c18 {
+  .c17 {
     -webkit-transition: 0.1s ease-in-out;
     transition: 0.1s ease-in-out;
   }
@@ -2112,11 +2098,7 @@ exports[`Calendar dates renders 1`] = `
                   className="c16"
                   size="medium"
                 >
-                  <span
-                    className="c17"
-                  >
-                    10
-                  </span>
+                  10
                 </div>
               </button>
             </div>
@@ -2147,7 +2129,7 @@ exports[`Calendar dates renders 1`] = `
             >
               <button
                 aria-label="Fri Jan 12 2018"
-                className="c18"
+                className="c17"
                 disabled={false}
                 onBlur={[Function]}
                 onClick={[Function]}
@@ -2158,11 +2140,7 @@ exports[`Calendar dates renders 1`] = `
                   className="c16"
                   size="medium"
                 >
-                  <span
-                    className="c17"
-                  >
-                    12
-                  </span>
+                  12
                 </div>
               </button>
             </div>
@@ -2911,12 +2889,6 @@ exports[`Calendar disabled renders 1`] = `
   padding: 12px;
 }
 
-.c17 {
-  font-size: 16px;
-  line-height: 1.375;
-  font-weight: bold;
-}
-
 .c6 {
   box-sizing: border-box;
   cursor: pointer;
@@ -2958,7 +2930,7 @@ exports[`Calendar disabled renders 1`] = `
   color: #000000;
 }
 
-.c18 {
+.c17 {
   box-sizing: border-box;
   cursor: pointer;
   outline: none;
@@ -2977,7 +2949,7 @@ exports[`Calendar disabled renders 1`] = `
   color: #000000;
 }
 
-.c18:hover {
+.c17:hover {
   background-color: rgba(221,221,221,0.5);
   color: #000000;
 }
@@ -3005,9 +2977,9 @@ exports[`Calendar disabled renders 1`] = `
 }
 
 .c12 {
-  -webkit-flex: 0 0;
-  -ms-flex: 0 0;
-  flex: 0 0;
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
 }
 
 .c14 {
@@ -3062,6 +3034,7 @@ exports[`Calendar disabled renders 1`] = `
   height: 54.857142857142854px;
   background-color: #865CD6;
   color: #FFFFFF;
+  font-weight: bold;
 }
 
 .c1 {
@@ -3143,7 +3116,7 @@ exports[`Calendar disabled renders 1`] = `
 }
 
 @media only screen and (min-width:700px) {
-  .c18 {
+  .c17 {
     -webkit-transition: 0.1s ease-in-out;
     transition: 0.1s ease-in-out;
   }
@@ -3503,11 +3476,7 @@ exports[`Calendar disabled renders 1`] = `
                   className="c16"
                   size="medium"
                 >
-                  <span
-                    className="c17"
-                  >
-                    10
-                  </span>
+                  10
                 </div>
               </button>
             </div>
@@ -3538,7 +3507,7 @@ exports[`Calendar disabled renders 1`] = `
             >
               <button
                 aria-label="Fri Jan 12 2018"
-                className="c18"
+                className="c17"
                 disabled={false}
                 onBlur={[Function]}
                 onClick={[Function]}
@@ -3549,11 +3518,7 @@ exports[`Calendar disabled renders 1`] = `
                   className="c16"
                   size="medium"
                 >
-                  <span
-                    className="c17"
-                  >
-                    12
-                  </span>
+                  12
                 </div>
               </button>
             </div>
@@ -4302,12 +4267,6 @@ exports[`Calendar firstDayOfWeek renders 1`] = `
   padding: 12px;
 }
 
-.c18 {
-  font-size: 16px;
-  line-height: 1.375;
-  font-weight: bold;
-}
-
 .c6 {
   box-sizing: border-box;
   cursor: pointer;
@@ -4396,9 +4355,9 @@ exports[`Calendar firstDayOfWeek renders 1`] = `
 }
 
 .c12 {
-  -webkit-flex: 0 0;
-  -ms-flex: 0 0;
-  flex: 0 0;
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
 }
 
 .c14 {
@@ -4453,6 +4412,7 @@ exports[`Calendar firstDayOfWeek renders 1`] = `
   height: 54.857142857142854px;
   background-color: #865CD6;
   color: #FFFFFF;
+  font-weight: bold;
 }
 
 .c1 {
@@ -5003,11 +4963,7 @@ exports[`Calendar firstDayOfWeek renders 1`] = `
                   className="c17"
                   size="medium"
                 >
-                  <span
-                    className="c18"
-                  >
-                    15
-                  </span>
+                  15
                 </div>
               </button>
             </div>
@@ -5976,11 +5932,7 @@ exports[`Calendar firstDayOfWeek renders 1`] = `
                   className="c17"
                   size="medium"
                 >
-                  <span
-                    className="c18"
-                  >
-                    15
-                  </span>
+                  15
                 </div>
               </button>
             </div>
@@ -6683,12 +6635,6 @@ exports[`Calendar renders 1`] = `
   padding: 12px;
 }
 
-.c18 {
-  font-size: 16px;
-  line-height: 1.375;
-  font-weight: bold;
-}
-
 .c6 {
   box-sizing: border-box;
   cursor: pointer;
@@ -6777,9 +6723,9 @@ exports[`Calendar renders 1`] = `
 }
 
 .c12 {
-  -webkit-flex: 0 0;
-  -ms-flex: 0 0;
-  flex: 0 0;
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
 }
 
 .c14 {
@@ -6834,6 +6780,7 @@ exports[`Calendar renders 1`] = `
   height: 54.857142857142854px;
   background-color: #865CD6;
   color: #FFFFFF;
+  font-weight: bold;
 }
 
 .c1 {
@@ -7384,11 +7331,7 @@ exports[`Calendar renders 1`] = `
                   className="c17"
                   size="medium"
                 >
-                  <span
-                    className="c18"
-                  >
-                    15
-                  </span>
+                  15
                 </div>
               </button>
             </div>
@@ -7970,7 +7913,7 @@ exports[`Calendar size renders 1`] = `
   stroke: none;
 }
 
-.c27 {
+.c26 {
   display: inline-block;
   -webkit-flex: 0 0 auto;
   -ms-flex: 0 0 auto;
@@ -7981,25 +7924,25 @@ exports[`Calendar size renders 1`] = `
   stroke: #666666;
 }
 
-.c27 g {
+.c26 g {
   fill: inherit;
   stroke: inherit;
 }
 
-.c27 *:not([stroke])[fill="none"] {
+.c26 *:not([stroke])[fill="none"] {
   stroke-width: 0;
 }
 
-.c27 *[stroke*="#"],
-.c27 *[STROKE*="#"] {
+.c26 *[stroke*="#"],
+.c26 *[STROKE*="#"] {
   stroke: inherit;
   fill: none;
 }
 
-.c27 *[fill-rule],
-.c27 *[FILL-RULE],
-.c27 *[fill*="#"],
-.c27 *[FILL*="#"] {
+.c26 *[fill-rule],
+.c26 *[FILL-RULE],
+.c26 *[fill*="#"],
+.c26 *[FILL*="#"] {
   fill: inherit;
   stroke: none;
 }
@@ -8104,12 +8047,6 @@ exports[`Calendar size renders 1`] = `
   padding: 12px;
 }
 
-.c18 {
-  font-size: 16px;
-  line-height: 1.375;
-  font-weight: bold;
-}
-
 .c6 {
   box-sizing: border-box;
   cursor: pointer;
@@ -8175,7 +8112,7 @@ exports[`Calendar size renders 1`] = `
   color: #000000;
 }
 
-.c21 {
+.c20 {
   overflow: hidden;
   height: 329.1428571428571px;
 }
@@ -8185,7 +8122,7 @@ exports[`Calendar size renders 1`] = `
   height: 164.57142857142856px;
 }
 
-.c28 {
+.c27 {
   overflow: hidden;
   height: 658.2857142857142px;
 }
@@ -8208,12 +8145,12 @@ exports[`Calendar size renders 1`] = `
 }
 
 .c12 {
-  -webkit-flex: 0 0;
-  -ms-flex: 0 0;
-  flex: 0 0;
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
 }
 
-.c22 {
+.c21 {
   display: -webkit-box;
   display: -webkit-flex;
   display: -ms-flexbox;
@@ -8231,7 +8168,7 @@ exports[`Calendar size renders 1`] = `
   opacity: 0.5;
 }
 
-.c23 {
+.c22 {
   display: -webkit-box;
   display: -webkit-flex;
   display: -ms-flexbox;
@@ -8248,7 +8185,7 @@ exports[`Calendar size renders 1`] = `
   height: 54.857142857142854px;
 }
 
-.c24 {
+.c23 {
   display: -webkit-box;
   display: -webkit-flex;
   display: -ms-flexbox;
@@ -8265,6 +8202,7 @@ exports[`Calendar size renders 1`] = `
   height: 54.857142857142854px;
   background-color: #865CD6;
   color: #FFFFFF;
+  font-weight: bold;
 }
 
 .c14 {
@@ -8319,9 +8257,10 @@ exports[`Calendar size renders 1`] = `
   height: 27.428571428571427px;
   background-color: #865CD6;
   color: #FFFFFF;
+  font-weight: bold;
 }
 
-.c29 {
+.c28 {
   display: -webkit-box;
   display: -webkit-flex;
   display: -ms-flexbox;
@@ -8339,7 +8278,7 @@ exports[`Calendar size renders 1`] = `
   opacity: 0.5;
 }
 
-.c30 {
+.c29 {
   display: -webkit-box;
   display: -webkit-flex;
   display: -ms-flexbox;
@@ -8356,7 +8295,7 @@ exports[`Calendar size renders 1`] = `
   height: 109.71428571428571px;
 }
 
-.c31 {
+.c30 {
   display: -webkit-box;
   display: -webkit-flex;
   display: -ms-flexbox;
@@ -8373,9 +8312,10 @@ exports[`Calendar size renders 1`] = `
   height: 109.71428571428571px;
   background-color: #865CD6;
   color: #FFFFFF;
+  font-weight: bold;
 }
 
-.c19 {
+.c18 {
   font-size: 22px;
   line-height: 1.45;
   width: 384px;
@@ -8387,13 +8327,13 @@ exports[`Calendar size renders 1`] = `
   width: 192px;
 }
 
-.c25 {
+.c24 {
   font-size: 36px;
   line-height: 1.11;
   width: 768px;
 }
 
-.c20 {
+.c19 {
   font-family: 'Zilla Slab','Work Sans',Arial,sans-serif;
   font-size: 24px;
   line-height: 1.333;
@@ -8413,7 +8353,7 @@ exports[`Calendar size renders 1`] = `
   margin-bottom: 0;
 }
 
-.c26 {
+.c25 {
   font-family: 'Zilla Slab','Work Sans',Arial,sans-serif;
   font-size: 36px;
   line-height: 1.23;
@@ -8493,7 +8433,7 @@ exports[`Calendar size renders 1`] = `
 }
 
 @media only screen and (max-width:699px) {
-  .c20 {
+  .c19 {
     font-size: 18px;
     line-height: 1.333;
     max-width: 432px;
@@ -8502,7 +8442,7 @@ exports[`Calendar size renders 1`] = `
 }
 
 @media only screen and (max-width:699px) {
-  .c20 {
+  .c19 {
     margin-top: 0;
     margin-bottom: 0;
   }
@@ -8525,7 +8465,7 @@ exports[`Calendar size renders 1`] = `
 }
 
 @media only screen and (max-width:699px) {
-  .c26 {
+  .c25 {
     font-size: 24px;
     line-height: 1.333;
     max-width: 576px;
@@ -8534,7 +8474,7 @@ exports[`Calendar size renders 1`] = `
 }
 
 @media only screen and (max-width:699px) {
-  .c26 {
+  .c25 {
     margin-top: 0;
     margin-bottom: 0;
   }
@@ -8977,11 +8917,7 @@ exports[`Calendar size renders 1`] = `
                   className="c17"
                   size="small"
                 >
-                  <span
-                    className="c18"
-                  >
-                    15
-                  </span>
+                  15
                 </div>
               </button>
             </div>
@@ -9528,7 +9464,7 @@ exports[`Calendar size renders 1`] = `
     </div>
   </div>
   <div
-    className="c19"
+    className="c18"
     size="medium"
   >
     <div
@@ -9539,7 +9475,7 @@ exports[`Calendar size renders 1`] = `
         className="c3"
       >
         <h3
-          className="c20"
+          className="c19"
           size="medium"
         >
           January 2018
@@ -9621,7 +9557,7 @@ exports[`Calendar size renders 1`] = `
         </div>
       </div>
       <div
-        className="c21"
+        className="c20"
         size="medium"
       >
         <div
@@ -9645,7 +9581,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c22"
+                  className="c21"
                   size="medium"
                 >
                   31
@@ -9666,7 +9602,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c23"
+                  className="c22"
                   size="medium"
                 >
                   1
@@ -9687,7 +9623,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c23"
+                  className="c22"
                   size="medium"
                 >
                   2
@@ -9708,7 +9644,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c23"
+                  className="c22"
                   size="medium"
                 >
                   3
@@ -9729,7 +9665,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c23"
+                  className="c22"
                   size="medium"
                 >
                   4
@@ -9750,7 +9686,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c23"
+                  className="c22"
                   size="medium"
                 >
                   5
@@ -9771,7 +9707,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c23"
+                  className="c22"
                   size="medium"
                 >
                   6
@@ -9796,7 +9732,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c23"
+                  className="c22"
                   size="medium"
                 >
                   7
@@ -9817,7 +9753,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c23"
+                  className="c22"
                   size="medium"
                 >
                   8
@@ -9838,7 +9774,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c23"
+                  className="c22"
                   size="medium"
                 >
                   9
@@ -9859,7 +9795,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c23"
+                  className="c22"
                   size="medium"
                 >
                   10
@@ -9880,7 +9816,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c23"
+                  className="c22"
                   size="medium"
                 >
                   11
@@ -9901,7 +9837,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c23"
+                  className="c22"
                   size="medium"
                 >
                   12
@@ -9922,7 +9858,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c23"
+                  className="c22"
                   size="medium"
                 >
                   13
@@ -9947,7 +9883,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c23"
+                  className="c22"
                   size="medium"
                 >
                   14
@@ -9968,14 +9904,10 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c24"
+                  className="c23"
                   size="medium"
                 >
-                  <span
-                    className="c18"
-                  >
-                    15
-                  </span>
+                  15
                 </div>
               </button>
             </div>
@@ -9993,7 +9925,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c23"
+                  className="c22"
                   size="medium"
                 >
                   16
@@ -10014,7 +9946,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c23"
+                  className="c22"
                   size="medium"
                 >
                   17
@@ -10035,7 +9967,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c23"
+                  className="c22"
                   size="medium"
                 >
                   18
@@ -10056,7 +9988,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c23"
+                  className="c22"
                   size="medium"
                 >
                   19
@@ -10077,7 +10009,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c23"
+                  className="c22"
                   size="medium"
                 >
                   20
@@ -10102,7 +10034,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c23"
+                  className="c22"
                   size="medium"
                 >
                   21
@@ -10123,7 +10055,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c23"
+                  className="c22"
                   size="medium"
                 >
                   22
@@ -10144,7 +10076,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c23"
+                  className="c22"
                   size="medium"
                 >
                   23
@@ -10165,7 +10097,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c23"
+                  className="c22"
                   size="medium"
                 >
                   24
@@ -10186,7 +10118,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c23"
+                  className="c22"
                   size="medium"
                 >
                   25
@@ -10207,7 +10139,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c23"
+                  className="c22"
                   size="medium"
                 >
                   26
@@ -10228,7 +10160,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c23"
+                  className="c22"
                   size="medium"
                 >
                   27
@@ -10253,7 +10185,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c23"
+                  className="c22"
                   size="medium"
                 >
                   28
@@ -10274,7 +10206,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c23"
+                  className="c22"
                   size="medium"
                 >
                   29
@@ -10295,7 +10227,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c23"
+                  className="c22"
                   size="medium"
                 >
                   30
@@ -10316,7 +10248,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c23"
+                  className="c22"
                   size="medium"
                 >
                   31
@@ -10337,7 +10269,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c22"
+                  className="c21"
                   size="medium"
                 >
                   1
@@ -10358,7 +10290,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c22"
+                  className="c21"
                   size="medium"
                 >
                   2
@@ -10379,7 +10311,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c22"
+                  className="c21"
                   size="medium"
                 >
                   3
@@ -10404,7 +10336,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c22"
+                  className="c21"
                   size="medium"
                 >
                   4
@@ -10425,7 +10357,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c22"
+                  className="c21"
                   size="medium"
                 >
                   5
@@ -10446,7 +10378,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c22"
+                  className="c21"
                   size="medium"
                 >
                   6
@@ -10467,7 +10399,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c22"
+                  className="c21"
                   size="medium"
                 >
                   7
@@ -10488,7 +10420,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c22"
+                  className="c21"
                   size="medium"
                 >
                   8
@@ -10509,7 +10441,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c22"
+                  className="c21"
                   size="medium"
                 >
                   9
@@ -10522,7 +10454,7 @@ exports[`Calendar size renders 1`] = `
     </div>
   </div>
   <div
-    className="c25"
+    className="c24"
     size="large"
   >
     <div
@@ -10533,7 +10465,7 @@ exports[`Calendar size renders 1`] = `
         className="c3"
       >
         <h3
-          className="c26"
+          className="c25"
           size="large"
         >
           January 2018
@@ -10559,7 +10491,7 @@ exports[`Calendar size renders 1`] = `
             >
               <svg
                 aria-label="Previous"
-                className="c27"
+                className="c26"
                 height="24px"
                 role="img"
                 size="large"
@@ -10595,7 +10527,7 @@ exports[`Calendar size renders 1`] = `
             >
               <svg
                 aria-label="Next"
-                className="c27"
+                className="c26"
                 height="24px"
                 role="img"
                 size="large"
@@ -10615,7 +10547,7 @@ exports[`Calendar size renders 1`] = `
         </div>
       </div>
       <div
-        className="c28"
+        className="c27"
         size="large"
       >
         <div
@@ -10639,7 +10571,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c29"
+                  className="c28"
                   size="large"
                 >
                   31
@@ -10660,7 +10592,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c30"
+                  className="c29"
                   size="large"
                 >
                   1
@@ -10681,7 +10613,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c30"
+                  className="c29"
                   size="large"
                 >
                   2
@@ -10702,7 +10634,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c30"
+                  className="c29"
                   size="large"
                 >
                   3
@@ -10723,7 +10655,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c30"
+                  className="c29"
                   size="large"
                 >
                   4
@@ -10744,7 +10676,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c30"
+                  className="c29"
                   size="large"
                 >
                   5
@@ -10765,7 +10697,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c30"
+                  className="c29"
                   size="large"
                 >
                   6
@@ -10790,7 +10722,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c30"
+                  className="c29"
                   size="large"
                 >
                   7
@@ -10811,7 +10743,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c30"
+                  className="c29"
                   size="large"
                 >
                   8
@@ -10832,7 +10764,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c30"
+                  className="c29"
                   size="large"
                 >
                   9
@@ -10853,7 +10785,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c30"
+                  className="c29"
                   size="large"
                 >
                   10
@@ -10874,7 +10806,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c30"
+                  className="c29"
                   size="large"
                 >
                   11
@@ -10895,7 +10827,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c30"
+                  className="c29"
                   size="large"
                 >
                   12
@@ -10916,7 +10848,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c30"
+                  className="c29"
                   size="large"
                 >
                   13
@@ -10941,7 +10873,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c30"
+                  className="c29"
                   size="large"
                 >
                   14
@@ -10962,14 +10894,10 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c31"
+                  className="c30"
                   size="large"
                 >
-                  <span
-                    className="c18"
-                  >
-                    15
-                  </span>
+                  15
                 </div>
               </button>
             </div>
@@ -10987,7 +10915,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c30"
+                  className="c29"
                   size="large"
                 >
                   16
@@ -11008,7 +10936,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c30"
+                  className="c29"
                   size="large"
                 >
                   17
@@ -11029,7 +10957,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c30"
+                  className="c29"
                   size="large"
                 >
                   18
@@ -11050,7 +10978,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c30"
+                  className="c29"
                   size="large"
                 >
                   19
@@ -11071,7 +10999,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c30"
+                  className="c29"
                   size="large"
                 >
                   20
@@ -11096,7 +11024,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c30"
+                  className="c29"
                   size="large"
                 >
                   21
@@ -11117,7 +11045,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c30"
+                  className="c29"
                   size="large"
                 >
                   22
@@ -11138,7 +11066,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c30"
+                  className="c29"
                   size="large"
                 >
                   23
@@ -11159,7 +11087,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c30"
+                  className="c29"
                   size="large"
                 >
                   24
@@ -11180,7 +11108,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c30"
+                  className="c29"
                   size="large"
                 >
                   25
@@ -11201,7 +11129,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c30"
+                  className="c29"
                   size="large"
                 >
                   26
@@ -11222,7 +11150,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c30"
+                  className="c29"
                   size="large"
                 >
                   27
@@ -11247,7 +11175,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c30"
+                  className="c29"
                   size="large"
                 >
                   28
@@ -11268,7 +11196,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c30"
+                  className="c29"
                   size="large"
                 >
                   29
@@ -11289,7 +11217,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c30"
+                  className="c29"
                   size="large"
                 >
                   30
@@ -11310,7 +11238,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c30"
+                  className="c29"
                   size="large"
                 >
                   31
@@ -11331,7 +11259,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c29"
+                  className="c28"
                   size="large"
                 >
                   1
@@ -11352,7 +11280,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c29"
+                  className="c28"
                   size="large"
                 >
                   2
@@ -11373,7 +11301,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c29"
+                  className="c28"
                   size="large"
                 >
                   3
@@ -11398,7 +11326,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c29"
+                  className="c28"
                   size="large"
                 >
                   4
@@ -11419,7 +11347,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c29"
+                  className="c28"
                   size="large"
                 >
                   5
@@ -11440,7 +11368,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c29"
+                  className="c28"
                   size="large"
                 >
                   6
@@ -11461,7 +11389,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c29"
+                  className="c28"
                   size="large"
                 >
                   7
@@ -11482,7 +11410,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c29"
+                  className="c28"
                   size="large"
                 >
                   8
@@ -11503,7 +11431,7 @@ exports[`Calendar size renders 1`] = `
                 type="button"
               >
                 <div
-                  className="c29"
+                  className="c28"
                   size="large"
                 >
                   9
diff --git a/src/js/components/Calendar/calendar.stories.js b/src/js/components/Calendar/calendar.stories.js
index 6cf6769f09d..7f33a217c09 100644
--- a/src/js/components/Calendar/calendar.stories.js
+++ b/src/js/components/Calendar/calendar.stories.js
@@ -19,5 +19,54 @@ class SimpleCalendar extends Component {
   }
 }
 
+class RichCalendar extends Component {
+  state = {}
+
+  onSelect = (selectedDate) => {
+    const { date, dates, previousSelectedDate } = this.state;
+    if (!dates) {
+      if (!date) {
+        this.setState({ date: selectedDate });
+      } else {
+        const priorDate = new Date(date);
+        const nextDate = new Date(selectedDate);
+        if (priorDate.getTime() < nextDate.getTime()) {
+          this.setState({ date: undefined, dates: [[date, selectedDate]] });
+        } else if (priorDate.getTime() > nextDate.getTime()) {
+          this.setState({ date: undefined, dates: [[selectedDate, date]] });
+        }
+      }
+    } else {
+      const priorDates = dates[0].map(d => new Date(d));
+      const previousDate = new Date(previousSelectedDate);
+      const nextDate = new Date(selectedDate);
+      if (nextDate.getTime() < previousDate.getTime()) {
+        if (nextDate.getTime() < priorDates[0].getTime()) {
+          this.setState({ dates: [[selectedDate, dates[0][1]]] });
+        } else if (nextDate.getTime() > priorDates[0].getTime()) {
+          this.setState({ dates: [[dates[0][0], selectedDate]] });
+        }
+      } else if (nextDate.getTime() > previousDate.getTime()) {
+        if (nextDate.getTime() > priorDates[1].getTime()) {
+          this.setState({ dates: [[dates[0][0], selectedDate]] });
+        } else if (nextDate.getTime() < priorDates[1].getTime()) {
+          this.setState({ dates: [[selectedDate, dates[0][1]]] });
+        }
+      }
+    }
+    this.setState({ previousSelectedDate: selectedDate });
+  }
+
+  render() {
+    const { date, dates } = this.state;
+    return (
+      <Grommet>
+        <Calendar date={date} dates={dates} onSelect={this.onSelect} />
+      </Grommet>
+    );
+  }
+}
+
 storiesOf('Calendar', module)
-  .add('Simple Calendar', () => <SimpleCalendar />);
+  .add('Simple Calendar', () => <SimpleCalendar />)
+  .add('Range Calendar', () => <RichCalendar />);
diff --git a/src/js/components/RangeSelector/__tests__/RangeSelector-test.js b/src/js/components/RangeSelector/__tests__/RangeSelector-test.js
index 4dddc3bd1be..8dacd7f8fb5 100644
--- a/src/js/components/RangeSelector/__tests__/RangeSelector-test.js
+++ b/src/js/components/RangeSelector/__tests__/RangeSelector-test.js
@@ -6,7 +6,7 @@ import { cleanup, render, Simulate } from 'react-testing-library';
 import { Grommet } from '../../Grommet';
 import { RangeSelector } from '../';
 
-describe('TextInput', () => {
+describe('RangeSelector', () => {
   afterEach(cleanup);
 
   test('basic', () => {
diff --git a/src/js/components/RangeSelector/__tests__/__snapshots__/RangeSelector-test.js.snap b/src/js/components/RangeSelector/__tests__/__snapshots__/RangeSelector-test.js.snap
index 9a315d94c85..49a96491204 100644
--- a/src/js/components/RangeSelector/__tests__/__snapshots__/RangeSelector-test.js.snap
+++ b/src/js/components/RangeSelector/__tests__/__snapshots__/RangeSelector-test.js.snap
@@ -1,6 +1,6 @@
 // Jest Snapshot v1, https://goo.gl/fbAQLP
 
-exports[`TextInput basic 1`] = `
+exports[`RangeSelector basic 1`] = `
 .c0 {
   font-family: 'Work Sans',Arial,sans-serif;
   font-size: 1em;
@@ -332,7 +332,7 @@ exports[`TextInput basic 1`] = `
 </div>
 `;
 
-exports[`TextInput color 1`] = `
+exports[`RangeSelector color 1`] = `
 .c0 {
   font-family: 'Work Sans',Arial,sans-serif;
   font-size: 1em;
@@ -664,7 +664,7 @@ exports[`TextInput color 1`] = `
 </div>
 `;
 
-exports[`TextInput direction 1`] = `
+exports[`RangeSelector direction 1`] = `
 .c0 {
   font-family: 'Work Sans',Arial,sans-serif;
   font-size: 1em;
@@ -1279,7 +1279,7 @@ exports[`TextInput direction 1`] = `
 </div>
 `;
 
-exports[`TextInput handle keyboard 1`] = `
+exports[`RangeSelector handle keyboard 1`] = `
 <div
   class="StyledGrommet-gKbdxL iuxDFS"
 >
@@ -1317,7 +1317,7 @@ exports[`TextInput handle keyboard 1`] = `
       </div>
     </div>
     <div
-      class="StyledBox-YaZNy inkIZG"
+      class="StyledBox-YaZNy hIUlFR"
       style="cursor: ew-resize;"
     />
     <div
@@ -1354,7 +1354,7 @@ exports[`TextInput handle keyboard 1`] = `
 </div>
 `;
 
-exports[`TextInput handle mouse 1`] = `
+exports[`RangeSelector handle mouse 1`] = `
 <div
   class="StyledGrommet-gKbdxL iuxDFS"
 >
@@ -1392,7 +1392,7 @@ exports[`TextInput handle mouse 1`] = `
       </div>
     </div>
     <div
-      class="StyledBox-YaZNy inkIZG"
+      class="StyledBox-YaZNy hIUlFR"
       style="cursor: ew-resize;"
     />
     <div
@@ -1429,7 +1429,7 @@ exports[`TextInput handle mouse 1`] = `
 </div>
 `;
 
-exports[`TextInput invert 1`] = `
+exports[`RangeSelector invert 1`] = `
 .c0 {
   font-family: 'Work Sans',Arial,sans-serif;
   font-size: 1em;
@@ -1906,7 +1906,7 @@ exports[`TextInput invert 1`] = `
 </div>
 `;
 
-exports[`TextInput max 1`] = `
+exports[`RangeSelector max 1`] = `
 .c0 {
   font-family: 'Work Sans',Arial,sans-serif;
   font-size: 1em;
@@ -2238,7 +2238,7 @@ exports[`TextInput max 1`] = `
 </div>
 `;
 
-exports[`TextInput min 1`] = `
+exports[`RangeSelector min 1`] = `
 .c0 {
   font-family: 'Work Sans',Arial,sans-serif;
   font-size: 1em;
@@ -2570,7 +2570,7 @@ exports[`TextInput min 1`] = `
 </div>
 `;
 
-exports[`TextInput opacity 1`] = `
+exports[`RangeSelector opacity 1`] = `
 .c0 {
   font-family: 'Work Sans',Arial,sans-serif;
   font-size: 1em;
@@ -2714,7 +2714,6 @@ exports[`TextInput opacity 1`] = `
   outline: none;
   max-width: 100%;
   background-color: rgba(134,92,214,0.1);
-  color: #FFFFFF;
   min-width: 0;
   min-height: 0;
   -webkit-flex-direction: column;
@@ -3192,7 +3191,7 @@ exports[`TextInput opacity 1`] = `
 </div>
 `;
 
-exports[`TextInput round 1`] = `
+exports[`RangeSelector round 1`] = `
 .c0 {
   font-family: 'Work Sans',Arial,sans-serif;
   font-size: 1em;
@@ -4294,7 +4293,7 @@ exports[`TextInput round 1`] = `
 </div>
 `;
 
-exports[`TextInput size 1`] = `
+exports[`RangeSelector size 1`] = `
 .c0 {
   font-family: 'Work Sans',Arial,sans-serif;
   font-size: 1em;
@@ -5304,7 +5303,7 @@ exports[`TextInput size 1`] = `
 </div>
 `;
 
-exports[`TextInput step 1`] = `
+exports[`RangeSelector step 1`] = `
 .c0 {
   font-family: 'Work Sans',Arial,sans-serif;
   font-size: 1em;
diff --git a/src/js/themes/vanilla.js b/src/js/themes/vanilla.js
index 3ba3b89029f..105f9574763 100644
--- a/src/js/themes/vanilla.js
+++ b/src/js/themes/vanilla.js
@@ -92,15 +92,15 @@ export default deepFreeze({
     borderSize: {
       xsmall: '1px',
       small: '2px',
-      medium: `${baseSpacing / 6}px`,
-      large: `${baseSpacing / 2}px`,
-      xlarge: `${baseSpacing}px`,
+      medium: `${baseSpacing / 6}px`, // 4
+      large: `${baseSpacing / 2}px`, // 12
+      xlarge: `${baseSpacing}px`, // 24
       narrow: {
         xsmall: '1px',
         small: '2px',
-        medium: `${baseSpacing / 6}px`,
-        large: `${baseSpacing / 4}px`,
-        xlarge: `${baseSpacing / 2}px`,
+        medium: `${baseSpacing / 6}px`, // 4
+        large: `${baseSpacing / 4}px`, // 6
+        xlarge: `${baseSpacing / 2}px`, // 12
       },
     },
     breakpoints: {
@@ -129,21 +129,21 @@ export default deepFreeze({
     edgeSize: {
       none: '0',
       hair: '1px', // for Chart
-      xxsmall: `${baseSpacing / 8}px`,
-      xsmall: `${baseSpacing / 4}px`,
-      small: `${baseSpacing / 2}px`,
-      medium: `${baseSpacing}px`,
-      large: `${baseSpacing * 2}px`,
-      xlarge: `${baseSpacing * 4}px`,
+      xxsmall: `${baseSpacing / 8}px`, // 3
+      xsmall: `${baseSpacing / 4}px`, // 6
+      small: `${baseSpacing / 2}px`, // 12
+      medium: `${baseSpacing}px`, // 24
+      large: `${baseSpacing * 2}px`, // 48
+      xlarge: `${baseSpacing * 4}px`, // 96
       narrow: {
         none: '0',
         hair: '1px', // for Chart
         xxsmall: '2px',
-        xsmall: `${baseSpacing / 8}px`,
-        small: `${baseSpacing / 4}px`,
-        medium: `${baseSpacing / 2}px`,
-        large: `${baseSpacing}px`,
-        xlarge: `${baseSpacing * 2}px`,
+        xsmall: `${baseSpacing / 8}px`, // 3
+        small: `${baseSpacing / 4}px`, // 6
+        medium: `${baseSpacing / 2}px`, // 12
+        large: `${baseSpacing}px`, // 24
+        xlarge: `${baseSpacing * 2}px`, // 48
       },
     },
     elevation: {
@@ -287,6 +287,7 @@ export default deepFreeze({
     },
   },
   calendar: {
+    // daySize must align with global.size
     small: {
       fontSize: '16px',
       lineHeight: 1.375,
@@ -391,9 +392,9 @@ export default deepFreeze({
     },
     digital: {
       text: {
-        medium: { size: '16px', height: 1.375 },
         xsmall: { size: '12px', height: 1.5 },
         small: { size: '14px', height: 1.43 },
+        medium: { size: '16px', height: 1.375 },
         large: { size: '24px', height: 1.167 },
         xlarge: { size: '32px', height: 1.1875 },
         xxlarge: { size: '48px', height: 1.125 },
@@ -495,8 +496,8 @@ export default deepFreeze({
   paragraph: {
     // maxWidth chosen to be ~50 characters wide
     // see: https://ux.stackexchange.com/a/34125
-    medium: { size: '16px', height: 1.375, maxWidth: `${baseSpacing * 16}px` },
     small: { size: '14px', height: 1.43, maxWidth: `${baseSpacing * 14}px` },
+    medium: { size: '16px', height: 1.375, maxWidth: `${baseSpacing * 16}px` },
     large: { size: '24px', height: 1.333, maxWidth: `${baseSpacing * 24}px` },
     xlarge: { size: '32px', height: 1.1875, maxWidth: `${baseSpacing * 32}px` },
   },
@@ -530,9 +531,9 @@ export default deepFreeze({
     step: 20,
   },
   text: {
-    medium: { size: '16px', height: 1.375 },
     xsmall: { size: '12px', height: 1.5 },
     small: { size: '14px', height: 1.43 },
+    medium: { size: '16px', height: 1.375 },
     large: { size: '24px', height: 1.167 },
     xlarge: { size: '32px', height: 1.1875 },
     xxlarge: { size: '48px', height: 1.125 },
diff --git a/src/js/utils/styles.js b/src/js/utils/styles.js
index e7dcd16614d..3804e9e397f 100644
--- a/src/js/utils/styles.js
+++ b/src/js/utils/styles.js
@@ -38,11 +38,13 @@ export const backgroundStyle = (background, theme) => {
       if (rgba) {
         return css`
           background-color: ${rgba};
-          color: ${
-            colorIsDark(rgba) ?
-            theme.global.colors.darkBackground.text :
-            theme.global.colors.lightBackground.text
-          };
+          ${(!background.opacity || background.opacity !== 'weak') &&
+            `color: ${
+              colorIsDark(rgba) ?
+              theme.global.colors.darkBackground.text :
+              theme.global.colors.lightBackground.text
+            };`
+          }
         `;
       }
     } else if (background.dark === false) {
