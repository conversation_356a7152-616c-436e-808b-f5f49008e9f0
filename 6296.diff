diff --git a/src/js/components/Accordion/__tests__/__snapshots__/Accordion-test.tsx.snap b/src/js/components/Accordion/__tests__/__snapshots__/Accordion-test.tsx.snap
index 88ee2d6c27a..18c4f85f9fe 100644
--- a/src/js/components/Accordion/__tests__/__snapshots__/Accordion-test.tsx.snap
+++ b/src/js/components/Accordion/__tests__/__snapshots__/Accordion-test.tsx.snap
@@ -209,6 +209,7 @@ exports[`Accordion AccordionPanel 1`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c0 {
@@ -556,6 +557,7 @@ exports[`Accordion accordion border 1`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c0 {
@@ -873,6 +875,7 @@ exports[`Accordion backward compatibility of hover.color = undefined 1`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c0 {
@@ -1184,6 +1187,7 @@ exports[`Accordion blur styles 1`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
   color: red;
 }
 
@@ -1493,6 +1497,7 @@ exports[`Accordion change active index 1`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c0 {
@@ -1895,6 +1900,7 @@ exports[`Accordion change active index 2`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c6 {
@@ -1902,6 +1908,7 @@ exports[`Accordion change active index 2`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
   color: #777777;
 }
 
@@ -2263,6 +2270,7 @@ exports[`Accordion change to second Panel 1`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c0 {
@@ -2687,6 +2695,7 @@ exports[`Accordion change to second Panel 2`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c12 {
@@ -2694,6 +2703,7 @@ exports[`Accordion change to second Panel 2`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
   color: #777777;
 }
 
@@ -3061,6 +3071,7 @@ exports[`Accordion change to second Panel without onActive 1`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c0 {
@@ -3461,6 +3472,7 @@ exports[`Accordion change to second Panel without onActive 2`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c11 {
@@ -3468,6 +3480,7 @@ exports[`Accordion change to second Panel without onActive 2`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
   color: #777777;
 }
 
@@ -4334,6 +4347,7 @@ exports[`Accordion custom accordion 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c0 {
@@ -4648,6 +4662,7 @@ exports[`Accordion custom accordion 2`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: break-word;
   color: #777777;
 }
 
@@ -4963,6 +4978,7 @@ exports[`Accordion focus and hover styles 1`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
   color: red;
 }
 
@@ -5277,6 +5293,7 @@ exports[`Accordion multiple panels 1`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c0 {
@@ -5677,6 +5694,7 @@ exports[`Accordion multiple panels 2`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c11 {
@@ -5684,6 +5702,7 @@ exports[`Accordion multiple panels 2`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
   color: #777777;
 }
 
@@ -6096,6 +6115,7 @@ exports[`Accordion multiple panels 3`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c6 {
@@ -6103,6 +6123,7 @@ exports[`Accordion multiple panels 3`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
   color: #777777;
 }
 
@@ -6518,6 +6539,7 @@ exports[`Accordion multiple panels 4`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c11 {
@@ -6525,6 +6547,7 @@ exports[`Accordion multiple panels 4`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
   color: #777777;
 }
 
@@ -6938,6 +6961,7 @@ exports[`Accordion multiple panels 5`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c6 {
@@ -6945,6 +6969,7 @@ exports[`Accordion multiple panels 5`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
   color: #777777;
 }
 
@@ -7605,6 +7630,7 @@ exports[`Accordion theme hover of hover.heading.color 1`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
   color: teal;
 }
 
@@ -7914,6 +7940,7 @@ exports[`Accordion wrapped panel 1`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c0 {
@@ -8314,6 +8341,7 @@ exports[`Accordion wrapped panel 2`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c6 {
@@ -8321,6 +8349,7 @@ exports[`Accordion wrapped panel 2`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
   color: #777777;
 }
 
diff --git a/src/js/components/Calendar/Calendar.js b/src/js/components/Calendar/Calendar.js
index 00ff88e48f3..95b96ea5460 100644
--- a/src/js/components/Calendar/Calendar.js
+++ b/src/js/components/Calendar/Calendar.js
@@ -565,6 +565,7 @@ const Calendar = forwardRef(
               }
               size={size}
               margin="none"
+              overflowWrap="normal"
             >
               {reference.toLocaleDateString(locale, {
                 month: 'long',
diff --git a/src/js/components/Calendar/__tests__/__snapshots__/Calendar-test.tsx.snap b/src/js/components/Calendar/__tests__/__snapshots__/Calendar-test.tsx.snap
index eb7ef5577ac..79025494d25 100644
--- a/src/js/components/Calendar/__tests__/__snapshots__/Calendar-test.tsx.snap
+++ b/src/js/components/Calendar/__tests__/__snapshots__/Calendar-test.tsx.snap
@@ -235,6 +235,7 @@ exports[`Calendar change months 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c1 {
@@ -1174,7 +1175,7 @@ exports[`Calendar change months 2`] = `
           class="StyledBox-sc-13pk1d4-0 fzaBin"
         >
           <h3
-            class="StyledHeading-sc-1rdh4aw-0 hAlxRY"
+            class="StyledHeading-sc-1rdh4aw-0 geeMiP"
           >
             January 2020
           </h3>
@@ -2628,6 +2629,7 @@ exports[`Calendar children 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c1 {
@@ -3469,6 +3471,7 @@ exports[`Calendar date 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c1 {
@@ -4646,6 +4649,7 @@ exports[`Calendar dates 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c1 {
@@ -5841,6 +5845,7 @@ exports[`Calendar daysOfWeek 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c1 {
@@ -7172,6 +7177,7 @@ exports[`Calendar disabled 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c1 {
@@ -8359,6 +8365,7 @@ exports[`Calendar fill 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c1 {
@@ -9549,6 +9556,7 @@ exports[`Calendar first day sunday week monday 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c1 {
@@ -10726,6 +10734,7 @@ exports[`Calendar firstDayOfWeek 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c1 {
@@ -13874,6 +13883,7 @@ exports[`Calendar reference 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c1 {
@@ -15031,6 +15041,7 @@ exports[`Calendar select date 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c1 {
@@ -15990,7 +16001,7 @@ exports[`Calendar select date 2`] = `
           class="StyledBox-sc-13pk1d4-0 fzaBin"
         >
           <h3
-            class="StyledHeading-sc-1rdh4aw-0 hAlxRY"
+            class="StyledHeading-sc-1rdh4aw-0 geeMiP"
           >
             January 2020
           </h3>
@@ -17031,6 +17042,7 @@ exports[`Calendar select dates 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c1 {
@@ -18008,7 +18020,7 @@ exports[`Calendar select dates 2`] = `
           class="StyledBox-sc-13pk1d4-0 fzaBin"
         >
           <h3
-            class="StyledHeading-sc-1rdh4aw-0 hAlxRY"
+            class="StyledHeading-sc-1rdh4aw-0 geeMiP"
           >
             January 2020
           </h3>
@@ -19049,6 +19061,7 @@ exports[`Calendar showAdjacentDays 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c1 {
@@ -21748,6 +21761,7 @@ exports[`Calendar size 1`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c19 {
@@ -21756,6 +21770,7 @@ exports[`Calendar size 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c26 {
@@ -21764,6 +21779,7 @@ exports[`Calendar size 1`] = `
   line-height: 40px;
   max-width: 816px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c1 {
diff --git a/src/js/components/DateInput/__tests__/__snapshots__/DateInput-test.tsx.snap b/src/js/components/DateInput/__tests__/__snapshots__/DateInput-test.tsx.snap
index cb8422cdf05..c64bc46c861 100644
--- a/src/js/components/DateInput/__tests__/__snapshots__/DateInput-test.tsx.snap
+++ b/src/js/components/DateInput/__tests__/__snapshots__/DateInput-test.tsx.snap
@@ -655,6 +655,7 @@ exports[`DateInput controlled format inline 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c7 {
@@ -1764,7 +1765,7 @@ exports[`DateInput controlled format inline 2`] = `
             class="StyledBox-sc-13pk1d4-0 fzaBin"
           >
             <h3
-              class="StyledHeading-sc-1rdh4aw-0 hAlxRY"
+              class="StyledHeading-sc-1rdh4aw-0 geeMiP"
             >
               July 2020
             </h3>
@@ -2970,6 +2971,7 @@ exports[`DateInput controlled format inline without timezone 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c7 {
@@ -4420,6 +4422,7 @@ exports[`DateInput controlled format inline without timezone 2`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c7 {
@@ -6903,6 +6906,7 @@ exports[`DateInput format inline 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c7 {
@@ -8869,6 +8873,7 @@ exports[`DateInput inline 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c1 {
@@ -11478,6 +11483,7 @@ exports[`DateInput range format inline 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c7 {
@@ -12791,6 +12797,7 @@ exports[`DateInput range inline 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c1 {
@@ -14968,6 +14975,7 @@ exports[`DateInput select format 3`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c2 {
@@ -15960,12 +15968,12 @@ exports[`DateInput select format 4`] = `
   }
 }
 @media only screen and (max-width: 768px) {
-  .hAlxRY {
+  .geeMiP {
     margin: 0px;
   }
 }
 @media only screen and (max-width: 768px) {
-  .hAlxRY {
+  .geeMiP {
     font-size: 18px;
     line-height: 24px;
     max-width: 432px;
@@ -16296,6 +16304,7 @@ exports[`DateInput select format inline 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c7 {
@@ -17399,7 +17408,7 @@ exports[`DateInput select format inline 2`] = `
             class="StyledBox-sc-13pk1d4-0 fzaBin"
           >
             <h3
-              class="StyledHeading-sc-1rdh4aw-0 hAlxRY"
+              class="StyledHeading-sc-1rdh4aw-0 geeMiP"
             >
               July 2020
             </h3>
@@ -18530,6 +18539,7 @@ exports[`DateInput select format inline 3`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c7 {
@@ -19965,6 +19975,7 @@ exports[`DateInput select format inline 4`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c7 {
@@ -21356,6 +21367,7 @@ exports[`DateInput select format inline range 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c7 {
@@ -22477,7 +22489,7 @@ exports[`DateInput select format inline range 2`] = `
             class="StyledBox-sc-13pk1d4-0 fzaBin"
           >
             <h3
-              class="StyledHeading-sc-1rdh4aw-0 hAlxRY"
+              class="StyledHeading-sc-1rdh4aw-0 geeMiP"
             >
               July 2020
             </h3>
@@ -23608,6 +23620,7 @@ exports[`DateInput select format inline range without timezone 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c7 {
@@ -25061,6 +25074,7 @@ exports[`DateInput select format inline range without timezone 2`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c7 {
@@ -26954,6 +26968,7 @@ exports[`DateInput select format no timezone 3`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c2 {
@@ -27946,12 +27961,12 @@ exports[`DateInput select format no timezone 4`] = `
   }
 }
 @media only screen and (max-width: 768px) {
-  .hAlxRY {
+  .geeMiP {
     margin: 0px;
   }
 }
 @media only screen and (max-width: 768px) {
-  .hAlxRY {
+  .geeMiP {
     font-size: 18px;
     line-height: 24px;
     max-width: 432px;
@@ -28204,6 +28219,7 @@ exports[`DateInput select inline 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c1 {
@@ -29382,6 +29398,7 @@ exports[`DateInput select inline without timezone 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c1 {
@@ -30638,6 +30655,7 @@ exports[`DateInput type format inline 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c7 {
@@ -31741,7 +31759,7 @@ exports[`DateInput type format inline 2`] = `
             class="StyledBox-sc-13pk1d4-0 fzaBin"
           >
             <h3
-              class="StyledHeading-sc-1rdh4aw-0 hAlxRY"
+              class="StyledHeading-sc-1rdh4aw-0 geeMiP"
             >
               July 2020
             </h3>
@@ -32871,6 +32889,7 @@ exports[`DateInput type format inline partial 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c7 {
@@ -34245,6 +34264,7 @@ exports[`DateInput type format inline partial without timezone 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c7 {
@@ -35619,6 +35639,7 @@ exports[`DateInput type format inline range 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c7 {
@@ -36740,7 +36761,7 @@ exports[`DateInput type format inline range 2`] = `
             class="StyledBox-sc-13pk1d4-0 fzaBin"
           >
             <h3
-              class="StyledHeading-sc-1rdh4aw-0 hAlxRY"
+              class="StyledHeading-sc-1rdh4aw-0 geeMiP"
             >
               July 2020
             </h3>
@@ -37870,6 +37891,7 @@ exports[`DateInput type format inline range partial 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c7 {
@@ -38991,7 +39013,7 @@ exports[`DateInput type format inline range partial 2`] = `
             class="StyledBox-sc-13pk1d4-0 fzaBin"
           >
             <h3
-              class="StyledHeading-sc-1rdh4aw-0 hAlxRY"
+              class="StyledHeading-sc-1rdh4aw-0 geeMiP"
             >
               July 2020
             </h3>
@@ -39851,7 +39873,7 @@ exports[`DateInput type format inline range partial 3`] = `
             class="StyledBox-sc-13pk1d4-0 fzaBin"
           >
             <h3
-              class="StyledHeading-sc-1rdh4aw-0 hAlxRY"
+              class="StyledHeading-sc-1rdh4aw-0 geeMiP"
             >
               July 2020
             </h3>
@@ -40982,6 +41004,7 @@ exports[`DateInput type format inline range partial without timezone 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c7 {
@@ -42375,6 +42398,7 @@ exports[`DateInput type format inline range partial without timezone 2`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c7 {
@@ -43750,6 +43774,7 @@ exports[`DateInput type format inline range partial without timezone 3`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c7 {
@@ -45125,6 +45150,7 @@ exports[`DateInput type format inline range without timezone 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c7 {
@@ -46518,6 +46544,7 @@ exports[`DateInput type format inline range without timezone 2`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c7 {
@@ -47910,6 +47937,7 @@ exports[`DateInput type format inline short 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c7 {
@@ -49013,7 +49041,7 @@ exports[`DateInput type format inline short 2`] = `
             class="StyledBox-sc-13pk1d4-0 fzaBin"
           >
             <h3
-              class="StyledHeading-sc-1rdh4aw-0 hAlxRY"
+              class="StyledHeading-sc-1rdh4aw-0 geeMiP"
             >
               July 2020
             </h3>
@@ -50144,6 +50172,7 @@ exports[`DateInput type format inline short without timezone 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c7 {
@@ -51519,6 +51548,7 @@ exports[`DateInput type format inline short without timezone 2`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c7 {
@@ -52894,6 +52924,7 @@ exports[`DateInput type format inline without timezone 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c7 {
@@ -54269,6 +54300,7 @@ exports[`DateInput type format inline without timezone 2`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: normal;
 }
 
 .c7 {
diff --git a/src/js/components/Heading/Heading.js b/src/js/components/Heading/Heading.js
index 869cc7107b1..796dadc2689 100644
--- a/src/js/components/Heading/Heading.js
+++ b/src/js/components/Heading/Heading.js
@@ -1,24 +1,53 @@
-import React, { forwardRef } from 'react';
+import React, { forwardRef, useState } from 'react';
+import { useLayoutEffect } from '../../utils/use-isomorphic-layout-effect';
 
 import { StyledHeading } from './StyledHeading';
 import { HeadingPropTypes } from './propTypes';
+import { useForwardedRef } from '../../utils';
 
 const Heading = forwardRef(
   (
-    { color, fill, level, weight, ...rest },
+    { color, fill, level, overflowWrap: overflowWrapProp, weight, ...rest },
+
     ref, // munged to avoid styled-components putting it in the DOM
-  ) => (
-    // enforce level to be a number
-    <StyledHeading
-      as={`h${level}`}
-      colorProp={color}
-      fillProp={fill}
-      level={+level}
-      weight={weight}
-      {...rest}
-      ref={ref}
-    />
-  ),
+  ) => {
+    const headingRef = useForwardedRef(ref);
+    const [overflowWrap, setOverflowWrap] = useState(
+      overflowWrapProp || 'break-word',
+    );
+
+    // handle overflowWrap of heading
+    useLayoutEffect(() => {
+      const updateOverflowWrap = () => {
+        let wrap;
+        if (!overflowWrapProp && headingRef.current) {
+          wrap =
+            headingRef.current.scrollWidth > headingRef.current.offsetWidth
+              ? 'anywhere'
+              : 'break-word';
+          setOverflowWrap(wrap);
+        }
+      };
+
+      window.addEventListener('resize', updateOverflowWrap);
+      updateOverflowWrap();
+      return () => window.removeEventListener('resize', updateOverflowWrap);
+    }, [headingRef, overflowWrapProp]);
+
+    return (
+      // enforce level to be a number
+      <StyledHeading
+        as={`h${level}`}
+        colorProp={color}
+        fillProp={fill}
+        level={+level}
+        overflowWrap={overflowWrap}
+        weight={weight}
+        {...rest}
+        ref={headingRef}
+      />
+    );
+  },
 );
 
 Heading.displayName = 'Heading';
diff --git a/src/js/components/Heading/StyledHeading.js b/src/js/components/Heading/StyledHeading.js
index 6bc3e7acf49..36b2befff7c 100644
--- a/src/js/components/Heading/StyledHeading.js
+++ b/src/js/components/Heading/StyledHeading.js
@@ -25,6 +25,7 @@ const sizeStyle = (props) => {
         font-weight: ${props.weight ||
         levelStyle.font.weight ||
         headingTheme.weight};
+        overflow-wrap: ${props.overflowWrap};
       `,
     ];
     if (props.responsive && headingTheme.responsiveBreakpoint) {
diff --git a/src/js/components/Heading/__tests__/__snapshots__/Heading-test.tsx.snap b/src/js/components/Heading/__tests__/__snapshots__/Heading-test.tsx.snap
index 4ebaaec2940..af8607f4c0c 100644
--- a/src/js/components/Heading/__tests__/__snapshots__/Heading-test.tsx.snap
+++ b/src/js/components/Heading/__tests__/__snapshots__/Heading-test.tsx.snap
@@ -16,6 +16,7 @@ exports[`Heading accepts ref 1`] = `
   line-height: 56px;
   max-width: 1200px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 @media only screen and (max-width:768px) {
@@ -51,6 +52,7 @@ exports[`Heading color renders 1`] = `
   line-height: 56px;
   max-width: 1200px;
   font-weight: 600;
+  overflow-wrap: break-word;
   color: #7D4CDB;
 }
 
@@ -87,6 +89,7 @@ exports[`Heading fill renders 1`] = `
   line-height: 56px;
   max-width: none;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 @media only screen and (max-width:768px) {
@@ -122,6 +125,7 @@ exports[`Heading level renders 1`] = `
   line-height: 56px;
   max-width: 1200px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c2 {
@@ -129,6 +133,7 @@ exports[`Heading level renders 1`] = `
   line-height: 40px;
   max-width: 816px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c3 {
@@ -136,6 +141,7 @@ exports[`Heading level renders 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c4 {
@@ -143,6 +149,7 @@ exports[`Heading level renders 1`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 @media only screen and (max-width:768px) {
@@ -224,6 +231,7 @@ exports[`Heading margin renders 1`] = `
   line-height: 56px;
   max-width: 1200px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c2 {
@@ -232,6 +240,7 @@ exports[`Heading margin renders 1`] = `
   line-height: 56px;
   max-width: 1200px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c3 {
@@ -240,6 +249,7 @@ exports[`Heading margin renders 1`] = `
   line-height: 56px;
   max-width: 1200px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c4 {
@@ -248,6 +258,7 @@ exports[`Heading margin renders 1`] = `
   line-height: 56px;
   max-width: 1200px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c5 {
@@ -256,6 +267,7 @@ exports[`Heading margin renders 1`] = `
   line-height: 56px;
   max-width: 1200px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c6 {
@@ -264,6 +276,7 @@ exports[`Heading margin renders 1`] = `
   line-height: 56px;
   max-width: 1200px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c7 {
@@ -272,6 +285,7 @@ exports[`Heading margin renders 1`] = `
   line-height: 56px;
   max-width: 1200px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c8 {
@@ -280,6 +294,7 @@ exports[`Heading margin renders 1`] = `
   line-height: 56px;
   max-width: 1200px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 @media only screen and (max-width:768px) {
@@ -440,6 +455,7 @@ exports[`Heading renders 1`] = `
   line-height: 56px;
   max-width: 1200px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 @media only screen and (max-width:768px) {
@@ -475,6 +491,7 @@ exports[`Heading size renders 1`] = `
   line-height: 40px;
   max-width: 816px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c2 {
@@ -482,6 +499,7 @@ exports[`Heading size renders 1`] = `
   line-height: 56px;
   max-width: 1200px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c3 {
@@ -489,6 +507,7 @@ exports[`Heading size renders 1`] = `
   line-height: 88px;
   max-width: 1968px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c4 {
@@ -496,6 +515,7 @@ exports[`Heading size renders 1`] = `
   line-height: 120px;
   max-width: 2736px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c5 {
@@ -503,6 +523,7 @@ exports[`Heading size renders 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c6 {
@@ -510,6 +531,7 @@ exports[`Heading size renders 1`] = `
   line-height: 72px;
   max-width: 1584px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c7 {
@@ -517,6 +539,7 @@ exports[`Heading size renders 1`] = `
   line-height: 28px;
   max-width: 528px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c8 {
@@ -524,6 +547,7 @@ exports[`Heading size renders 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c9 {
@@ -531,6 +555,7 @@ exports[`Heading size renders 1`] = `
   line-height: 40px;
   max-width: 816px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c10 {
@@ -538,6 +563,7 @@ exports[`Heading size renders 1`] = `
   line-height: 48px;
   max-width: 1008px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c11 {
@@ -545,6 +571,7 @@ exports[`Heading size renders 1`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c12 {
@@ -552,6 +579,7 @@ exports[`Heading size renders 1`] = `
   line-height: normal;
   max-width: 1200px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 @media only screen and (max-width:768px) {
@@ -715,6 +743,7 @@ exports[`Heading textAlign renders 1`] = `
   line-height: 56px;
   max-width: 1200px;
   font-weight: 600;
+  overflow-wrap: break-word;
   text-align: left;
 }
 
@@ -723,6 +752,7 @@ exports[`Heading textAlign renders 1`] = `
   line-height: 56px;
   max-width: 1200px;
   font-weight: 600;
+  overflow-wrap: break-word;
   text-align: center;
 }
 
@@ -731,6 +761,7 @@ exports[`Heading textAlign renders 1`] = `
   line-height: 56px;
   max-width: 1200px;
   font-weight: 600;
+  overflow-wrap: break-word;
   text-align: right;
 }
 
@@ -739,6 +770,7 @@ exports[`Heading textAlign renders 1`] = `
   line-height: 56px;
   max-width: 1200px;
   font-weight: 600;
+  overflow-wrap: break-word;
   text-align: justify;
 }
 
@@ -808,6 +840,7 @@ exports[`Heading truncate renders 1`] = `
   line-height: 56px;
   max-width: 1200px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c2 {
@@ -815,6 +848,7 @@ exports[`Heading truncate renders 1`] = `
   line-height: 56px;
   max-width: 1200px;
   font-weight: 600;
+  overflow-wrap: break-word;
   white-space: nowrap;
   overflow: hidden;
   text-overflow: ellipsis;
@@ -869,6 +903,7 @@ exports[`Theme based font family renders 1`] = `
   line-height: 56px;
   max-width: 1200px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c2 {
@@ -877,6 +912,7 @@ exports[`Theme based font family renders 1`] = `
   line-height: 40px;
   max-width: 816px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c3 {
@@ -885,6 +921,7 @@ exports[`Theme based font family renders 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c4 {
@@ -893,6 +930,7 @@ exports[`Theme based font family renders 1`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 @media only screen and (max-width:768px) {
@@ -961,6 +999,7 @@ exports[`Theme based font weight renders 1`] = `
   line-height: 56px;
   max-width: 1200px;
   font-weight: 700;
+  overflow-wrap: break-word;
 }
 
 .c2 {
@@ -968,6 +1007,7 @@ exports[`Theme based font weight renders 1`] = `
   line-height: 40px;
   max-width: 816px;
   font-weight: 400;
+  overflow-wrap: break-word;
 }
 
 .c3 {
@@ -975,6 +1015,7 @@ exports[`Theme based font weight renders 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 200;
+  overflow-wrap: break-word;
 }
 
 .c4 {
@@ -982,6 +1023,7 @@ exports[`Theme based font weight renders 1`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 @media only screen and (max-width:768px) {
@@ -1050,6 +1092,7 @@ exports[`Theme color renders 1`] = `
   line-height: 56px;
   max-width: 1200px;
   font-weight: 600;
+  overflow-wrap: break-word;
   color: #000000;
 }
 
@@ -1058,6 +1101,7 @@ exports[`Theme color renders 1`] = `
   line-height: 40px;
   max-width: 816px;
   font-weight: 600;
+  overflow-wrap: break-word;
   color: #000000;
 }
 
@@ -1066,6 +1110,7 @@ exports[`Theme color renders 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: break-word;
   color: #000000;
 }
 
@@ -1074,6 +1119,7 @@ exports[`Theme color renders 1`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
   color: #000000;
 }
 
@@ -1143,6 +1189,7 @@ exports[`responsive renders 1`] = `
   line-height: 56px;
   max-width: 1200px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c2 {
@@ -1150,6 +1197,7 @@ exports[`responsive renders 1`] = `
   line-height: 56px;
   max-width: 1200px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 @media only screen and (max-width:768px) {
@@ -1189,6 +1237,7 @@ exports[`weight renders 1`] = `
   line-height: 56px;
   max-width: 1200px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c2 {
@@ -1196,6 +1245,7 @@ exports[`weight renders 1`] = `
   line-height: 56px;
   max-width: 1200px;
   font-weight: normal;
+  overflow-wrap: break-word;
 }
 
 .c3 {
@@ -1203,6 +1253,7 @@ exports[`weight renders 1`] = `
   line-height: 56px;
   max-width: 1200px;
   font-weight: bold;
+  overflow-wrap: break-word;
 }
 
 .c4 {
@@ -1210,6 +1261,7 @@ exports[`weight renders 1`] = `
   line-height: 56px;
   max-width: 1200px;
   font-weight: 700;
+  overflow-wrap: break-word;
 }
 
 @media only screen and (max-width:768px) {
diff --git a/src/js/components/Heading/index.d.ts b/src/js/components/Heading/index.d.ts
index 450bee83613..27c26330036 100644
--- a/src/js/components/Heading/index.d.ts
+++ b/src/js/components/Heading/index.d.ts
@@ -19,6 +19,7 @@ export interface HeadingProps {
   fill?: boolean;
   level?: '1' | '2' | '3' | '4' | '5' | '6' | 1 | 2 | 3 | 4 | 5 | 6;
   margin?: MarginType;
+  overflowWrap?: 'normal' | 'break-word' | 'anywhere' | string;
   responsive?: boolean;
   size?: 'small' | 'medium' | 'large' | 'xlarge' | string;
   textAlign?: TextAlignType;
diff --git a/src/js/components/Heading/propTypes.js b/src/js/components/Heading/propTypes.js
index 4ed76993fec..7e5a1f22781 100644
--- a/src/js/components/Heading/propTypes.js
+++ b/src/js/components/Heading/propTypes.js
@@ -8,6 +8,10 @@ if (process.env.NODE_ENV !== 'production') {
     color: colorPropType,
     fill: PropTypes.bool,
     level: PropTypes.oneOf([1, 2, 3, 4, 5, 6, '1', '2', '3', '4', '5', '6']),
+    overflowWrap: PropTypes.oneOfType([
+      PropTypes.oneOf(['normal', 'break-word', 'anywhere']),
+      PropTypes.string,
+    ]),
     responsive: PropTypes.bool,
     size: PropTypes.oneOfType([
       PropTypes.oneOf(['small', 'medium', 'large', 'xlarge']),
diff --git a/src/js/components/Heading/stories/All.js b/src/js/components/Heading/stories/All.js
index b229fc1a4c9..79e4d6a2b98 100644
--- a/src/js/components/Heading/stories/All.js
+++ b/src/js/components/Heading/stories/All.js
@@ -8,6 +8,8 @@ Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
 tempor incididunt ut labore et dolore magna aliqua.
 `;
 
+const longWord = 'Supercalifragilisticexpialidocious';
+
 const H = ({ level, size }) => (
   <Heading level={level} size={size}>
     {`Heading ${level} ${size}`}
@@ -42,6 +44,7 @@ export const All = () => (
       <Set size="xlarge" />
     </Grid>
     <Heading fill>{headingFiller}</Heading>
+    <Heading fill>{longWord}</Heading>
   </>
   // </Grommet>
 );
diff --git a/src/js/components/Markdown/__tests__/__snapshots__/Markdown-test.js.snap b/src/js/components/Markdown/__tests__/__snapshots__/Markdown-test.js.snap
index 0eef3ff222d..50a7cabf20e 100644
--- a/src/js/components/Markdown/__tests__/__snapshots__/Markdown-test.js.snap
+++ b/src/js/components/Markdown/__tests__/__snapshots__/Markdown-test.js.snap
@@ -16,6 +16,7 @@ exports[`Markdown renders 1`] = `
   line-height: 56px;
   max-width: 1200px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c3 {
@@ -23,6 +24,7 @@ exports[`Markdown renders 1`] = `
   line-height: 40px;
   max-width: 816px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c4 {
@@ -30,6 +32,7 @@ exports[`Markdown renders 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c5 {
@@ -37,6 +40,7 @@ exports[`Markdown renders 1`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c2 {
@@ -305,6 +309,7 @@ exports[`wrapper 1`] = `
   line-height: 56px;
   max-width: 1200px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c5 {
@@ -312,6 +317,7 @@ exports[`wrapper 1`] = `
   line-height: 40px;
   max-width: 816px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c6 {
@@ -319,6 +325,7 @@ exports[`wrapper 1`] = `
   line-height: 32px;
   max-width: 624px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c7 {
@@ -326,6 +333,7 @@ exports[`wrapper 1`] = `
   line-height: 24px;
   max-width: 432px;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c4 {
diff --git a/src/js/components/PageHeader/__tests__/__snapshots__/PageHeader-test.tsx.snap b/src/js/components/PageHeader/__tests__/__snapshots__/PageHeader-test.tsx.snap
index 11cf0bfcdc5..263fda3aff6 100644
--- a/src/js/components/PageHeader/__tests__/__snapshots__/PageHeader-test.tsx.snap
+++ b/src/js/components/PageHeader/__tests__/__snapshots__/PageHeader-test.tsx.snap
@@ -201,6 +201,7 @@ exports[`PageHeader basic 1`] = `
   line-height: 56px;
   max-width: none;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c2 {
@@ -518,6 +519,7 @@ exports[`PageHeader custom theme 1`] = `
   line-height: 56px;
   max-width: none;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c2 {
@@ -820,6 +822,7 @@ exports[`PageHeader size - large 1`] = `
   line-height: 88px;
   max-width: none;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c2 {
@@ -1120,6 +1123,7 @@ exports[`PageHeader size - medium 1`] = `
   line-height: 56px;
   max-width: none;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c2 {
@@ -1420,6 +1424,7 @@ exports[`PageHeader size - small 1`] = `
   line-height: 40px;
   max-width: none;
   font-weight: 600;
+  overflow-wrap: break-word;
 }
 
 .c2 {
diff --git a/src/js/components/__tests__/__snapshots__/components-test.js.snap b/src/js/components/__tests__/__snapshots__/components-test.js.snap
index f70fa79ba5a..d41c2888626 100644
--- a/src/js/components/__tests__/__snapshots__/components-test.js.snap
+++ b/src/js/components/__tests__/__snapshots__/components-test.js.snap
@@ -468,6 +468,7 @@ Object {
       "gridArea": [Function],
       "level": [Function],
       "margin": [Function],
+      "overflowWrap": [Function],
       "responsive": [Function],
       "size": [Function],
       "textAlign": [Function],
