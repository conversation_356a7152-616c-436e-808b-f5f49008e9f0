diff --git a/src/js/components/Form/Form.js b/src/js/components/Form/Form.js
index dc80434750e..bb5abc0cdb3 100644
--- a/src/js/components/Form/Form.js
+++ b/src/js/components/Form/Form.js
@@ -497,7 +497,13 @@ const Form = forwardRef(
               validateArg,
               required,
             );
-            return () => delete validationRulesRef.current[name].field;
+            return () => {
+              delete validationRulesRef.current[name];
+              const requiredFieldIndex = requiredFields.current.indexOf(name);
+              if (requiredFieldIndex !== -1) {
+                requiredFields.current.splice(requiredFieldIndex, 1);
+              }
+            };
           }
 
           return undefined;
diff --git a/src/js/components/Form/__tests__/Form-test-controlled.js b/src/js/components/Form/__tests__/Form-test-controlled.js
index 64b2ba7f9b1..2e5766cf73f 100644
--- a/src/js/components/Form/__tests__/Form-test-controlled.js
+++ b/src/js/components/Form/__tests__/Form-test-controlled.js
@@ -482,6 +482,7 @@ describe('Form controlled', () => {
       expect.objectContaining({
         errors: { mood: 'required' },
         infos: {},
+        valid: false,
       }),
     );
 
@@ -491,7 +492,7 @@ describe('Form controlled', () => {
     act(() => toggleField.focus());
     act(() => jest.advanceTimersByTime(200)); // allow validations to run
     expect(onValidate).toHaveBeenLastCalledWith(
-      expect.objectContaining({ errors: {}, infos: {} }),
+      expect.objectContaining({ errors: {}, infos: {}, valid: true }),
     );
 
     // clear mood, should fail validation
@@ -503,6 +504,7 @@ describe('Form controlled', () => {
       expect.objectContaining({
         errors: { mood: 'required' },
         infos: {},
+        valid: false,
       }),
     );
 
@@ -513,7 +515,7 @@ describe('Form controlled', () => {
     act(() => toggleField.focus());
     act(() => jest.advanceTimersByTime(200)); // allow validations to run
     expect(onValidate).toHaveBeenLastCalledWith(
-      expect.objectContaining({ errors: {}, infos: {} }),
+      expect.objectContaining({ errors: {}, infos: {}, valid: true }),
     );
 
     expect(container.firstChild).toMatchSnapshot();
diff --git a/src/js/components/Form/__tests__/Form-test-uncontrolled.js b/src/js/components/Form/__tests__/Form-test-uncontrolled.js
index b31f772ce58..61db8f65308 100644
--- a/src/js/components/Form/__tests__/Form-test-uncontrolled.js
+++ b/src/js/components/Form/__tests__/Form-test-uncontrolled.js
@@ -1389,6 +1389,7 @@ describe('Form uncontrolled', () => {
       expect.objectContaining({
         errors: { mood: 'required' },
         infos: {},
+        valid: false,
       }),
     );
 
@@ -1398,7 +1399,7 @@ describe('Form uncontrolled', () => {
     act(() => toggleField.focus());
     act(() => jest.advanceTimersByTime(200)); // allow validations to run
     expect(onValidate).toHaveBeenLastCalledWith(
-      expect.objectContaining({ errors: {}, infos: {} }),
+      expect.objectContaining({ errors: {}, infos: {}, valid: true }),
     );
 
     // clear mood, should fail validation
@@ -1410,6 +1411,7 @@ describe('Form uncontrolled', () => {
       expect.objectContaining({
         errors: { mood: 'required' },
         infos: {},
+        valid: false,
       }),
     );
 
@@ -1420,7 +1422,7 @@ describe('Form uncontrolled', () => {
     act(() => toggleField.focus());
     act(() => jest.advanceTimersByTime(200)); // allow validations to run
     expect(onValidate).toHaveBeenLastCalledWith(
-      expect.objectContaining({ errors: {}, infos: {} }),
+      expect.objectContaining({ errors: {}, infos: {}, valid: true }),
     );
 
     expect(container.firstChild).toMatchSnapshot();
