diff --git a/src/js/components/Data/__tests__/__snapshots__/Data-test.tsx.snap b/src/js/components/Data/__tests__/__snapshots__/Data-test.tsx.snap
index b96e4467cdd..b16ba2a4382 100644
--- a/src/js/components/Data/__tests__/__snapshots__/Data-test.tsx.snap
+++ b/src/js/components/Data/__tests__/__snapshots__/Data-test.tsx.snap
@@ -174,6 +174,11 @@ exports[`Data controlled search 1`] = `
   padding: 12px;
 }
 
+.c8:hover {
+  background-color: rgba(221,221,221,0.4);
+  color: #000000;
+}
+
 .c8:focus {
   outline: none;
   box-shadow: 0 0 2px 2px #6FFFB0;
@@ -407,6 +412,7 @@ exports[`Data controlled search 1`] = `
         <button
           aria-label="Open filters"
           class="c8"
+          id="data--filters-control"
           type="button"
         >
           <svg
@@ -580,7 +586,8 @@ exports[`Data controlled search 2`] = `
       >
         <button
           aria-label="Open filters"
-          class="StyledButton-sc-323bzc-0 emhJYD"
+          class="StyledButton-sc-323bzc-0 wfYUD"
+          id="data--filters-control"
           type="button"
         >
           <svg
@@ -882,6 +889,11 @@ exports[`Data messages 1`] = `
   padding: 12px;
 }
 
+.c8:hover {
+  background-color: rgba(221,221,221,0.4);
+  color: #000000;
+}
+
 .c8:focus {
   outline: none;
   box-shadow: 0 0 2px 2px #6FFFB0;
@@ -1115,6 +1127,7 @@ exports[`Data messages 1`] = `
         <button
           aria-label="Open filters"
           class="c8"
+          id="data--filters-control"
           type="button"
         >
           <svg
@@ -3254,6 +3267,11 @@ exports[`Data toolbar 1`] = `
   padding: 12px;
 }
 
+.c8:hover {
+  background-color: rgba(221,221,221,0.4);
+  color: #000000;
+}
+
 .c8:focus {
   outline: none;
   box-shadow: 0 0 2px 2px #6FFFB0;
@@ -3487,6 +3505,7 @@ exports[`Data toolbar 1`] = `
         <button
           aria-label="Open filters"
           class="c8"
+          id="data--filters-control"
           type="button"
         >
           <svg
@@ -3831,6 +3850,11 @@ exports[`Data toolbar filters 1`] = `
   padding: 12px;
 }
 
+.c4:hover {
+  background-color: rgba(221,221,221,0.4);
+  color: #000000;
+}
+
 .c4:focus {
   outline: none;
   box-shadow: 0 0 2px 2px #6FFFB0;
@@ -3943,6 +3967,7 @@ exports[`Data toolbar filters 1`] = `
         <button
           aria-label="Open filters"
           class="c4"
+          id="data--filters-control"
           type="button"
         >
           <svg
@@ -4669,6 +4694,11 @@ exports[`Data uncontrolled search 1`] = `
   padding: 12px;
 }
 
+.c8:hover {
+  background-color: rgba(221,221,221,0.4);
+  color: #000000;
+}
+
 .c8:focus {
   outline: none;
   box-shadow: 0 0 2px 2px #6FFFB0;
@@ -4902,6 +4932,7 @@ exports[`Data uncontrolled search 1`] = `
         <button
           aria-label="Open filters"
           class="c8"
+          id="data--filters-control"
           type="button"
         >
           <svg
@@ -5075,7 +5106,8 @@ exports[`Data uncontrolled search 2`] = `
       >
         <button
           aria-label="Open filters"
-          class="StyledButton-sc-323bzc-0 emhJYD"
+          class="StyledButton-sc-323bzc-0 wfYUD"
+          id="data--filters-control"
           type="button"
         >
           <svg
@@ -5597,6 +5629,11 @@ exports[`Data view all 1`] = `
   padding: 12px;
 }
 
+.c8:hover {
+  background-color: rgba(221,221,221,0.4);
+  color: #000000;
+}
+
 .c8:focus {
   outline: none;
   box-shadow: 0 0 2px 2px #6FFFB0;
@@ -5830,6 +5867,7 @@ exports[`Data view all 1`] = `
         <button
           aria-label="Open filters"
           class="c8"
+          id="data--filters-control"
           type="button"
         >
           <svg
@@ -6098,6 +6136,11 @@ exports[`Data view property option 1`] = `
   padding: 12px;
 }
 
+.c8:hover {
+  background-color: rgba(221,221,221,0.4);
+  color: #000000;
+}
+
 .c8:focus {
   outline: none;
   box-shadow: 0 0 2px 2px #6FFFB0;
@@ -6331,6 +6374,7 @@ exports[`Data view property option 1`] = `
         <button
           aria-label="Open filters"
           class="c8"
+          id="data--filters-control"
           type="button"
         >
           <svg
@@ -6599,6 +6643,11 @@ exports[`Data view property range 1`] = `
   padding: 12px;
 }
 
+.c8:hover {
+  background-color: rgba(221,221,221,0.4);
+  color: #000000;
+}
+
 .c8:focus {
   outline: none;
   box-shadow: 0 0 2px 2px #6FFFB0;
@@ -6832,6 +6881,7 @@ exports[`Data view property range 1`] = `
         <button
           aria-label="Open filters"
           class="c8"
+          id="data--filters-control"
           type="button"
         >
           <svg
@@ -7121,6 +7171,11 @@ exports[`Data view search 1`] = `
   padding: 12px;
 }
 
+.c8:hover {
+  background-color: rgba(221,221,221,0.4);
+  color: #000000;
+}
+
 .c8:focus {
   outline: none;
   box-shadow: 0 0 2px 2px #6FFFB0;
@@ -7354,6 +7409,7 @@ exports[`Data view search 1`] = `
         <button
           aria-label="Open filters"
           class="c8"
+          id="data--filters-control"
           type="button"
         >
           <svg
@@ -7622,6 +7678,11 @@ exports[`Data view sort 1`] = `
   padding: 12px;
 }
 
+.c8:hover {
+  background-color: rgba(221,221,221,0.4);
+  color: #000000;
+}
+
 .c8:focus {
   outline: none;
   box-shadow: 0 0 2px 2px #6FFFB0;
@@ -7855,6 +7916,7 @@ exports[`Data view sort 1`] = `
         <button
           aria-label="Open filters"
           class="c8"
+          id="data--filters-control"
           type="button"
         >
           <svg
diff --git a/src/js/components/DataFilters/DataFilters.js b/src/js/components/DataFilters/DataFilters.js
index c1fa3bf5ffc..a672b0c5825 100644
--- a/src/js/components/DataFilters/DataFilters.js
+++ b/src/js/components/DataFilters/DataFilters.js
@@ -1,5 +1,6 @@
 import React, { Children, useContext, useMemo, useState } from 'react';
 import { Filter } from 'grommet-icons/icons/Filter';
+import { FormClose } from 'grommet-icons/icons/FormClose';
 import { Box } from '../Box';
 import { Button } from '../Button';
 import { DataFilter } from '../DataFilter';
@@ -8,6 +9,7 @@ import { DataSort } from '../DataSort';
 import { DropButton } from '../DropButton';
 import { Header } from '../Header';
 import { Heading } from '../Heading';
+import { Layer } from '../Layer';
 import { DataContext } from '../../contexts/DataContext';
 import { MessageContext } from '../../contexts/MessageContext';
 import { DataFiltersPropTypes } from './propTypes';
@@ -16,9 +18,20 @@ const dropProps = {
   align: { top: 'bottom', right: 'right' },
 };
 
-export const DataFilters = ({ drop, children, heading, ...rest }) => {
-  const { clearFilters, data, messages, properties, view } =
-    useContext(DataContext);
+const layerProps = {
+  full: 'vertical',
+  position: 'right',
+};
+
+export const DataFilters = ({ drop, children, heading, layer, ...rest }) => {
+  const {
+    clearFilters,
+    data,
+    id: dataId,
+    messages,
+    properties,
+    view,
+  } = useContext(DataContext);
   const { format } = useContext(MessageContext);
   const [showContent, setShowContent] = useState();
   // touched is a map of form field name to its value, it only has fields that
@@ -26,7 +39,7 @@ export const DataFilters = ({ drop, children, heading, ...rest }) => {
   // on what's inside DataFilters as opposed to trying to track from the view
   // object.
   const [touched, setTouched] = useState({});
-  const controlled = useMemo(() => drop, [drop]);
+  const controlled = useMemo(() => drop || layer, [drop, layer]);
   // generate the badge value based on touched fields that have a value
   const badge = useMemo(
     () =>
@@ -99,6 +112,13 @@ export const DataFilters = ({ drop, children, heading, ...rest }) => {
               })}
           </Heading>
           {!controlled && clearControl}
+          {layer && (
+            <Button
+              icon={<FormClose />}
+              hoverIndicator
+              onClick={() => setShowContent(undefined)}
+            />
+          )}
         </Header>
       )}
       {filters}
@@ -109,27 +129,57 @@ export const DataFilters = ({ drop, children, heading, ...rest }) => {
   if (!controlled) return content;
 
   // drop
-  const control = (
-    <DropButton
-      aria-label={format({
-        id: 'dataFilters.open',
-        messages: messages?.dataFilters,
-      })}
-      kind="toolbar"
-      icon={<Filter />}
-      dropProps={dropProps}
-      dropContent={content}
-      badge={badge}
-      open={showContent}
-      onOpen={() => setShowContent(undefined)}
-      onClose={() => setShowContent(undefined)}
-    />
-  );
+  let control;
+  if (drop) {
+    control = (
+      <DropButton
+        id={`${dataId}--filters-control`}
+        aria-label={format({
+          id: 'dataFilters.open',
+          messages: messages?.dataFilters,
+        })}
+        kind="toolbar"
+        icon={<Filter />}
+        hoverIndicator
+        dropProps={dropProps}
+        dropContent={content}
+        badge={badge}
+        open={showContent}
+        onOpen={() => setShowContent(undefined)}
+        onClose={() => setShowContent(undefined)}
+      />
+    );
+  } else if (layer) {
+    control = (
+      <Button
+        id={`${dataId}--filters-control`}
+        aria-label={format({
+          id: 'dataFilters.open',
+          messages: messages?.dataFilters,
+        })}
+        kind="toolbar"
+        hoverIndicator
+        icon={<Filter />}
+        badge={badge}
+        onClick={() => setShowContent(true)}
+      />
+    );
+  }
 
   return (
     <Box flex={false} direction="row" gap="small" {...rest}>
       {control}
       {clearControl}
+      {layer && showContent && (
+        <Layer
+          id={`${dataId}--filters-layer`}
+          {...(typeof layer === 'object' ? layer : layerProps)}
+          onClickOutside={() => setShowContent(undefined)}
+          onEsc={() => setShowContent(undefined)}
+        >
+          {content}
+        </Layer>
+      )}
     </Box>
   );
 };
diff --git a/src/js/components/DataFilters/__tests__/DataFilters-test.tsx b/src/js/components/DataFilters/__tests__/DataFilters-test.tsx
index 46f93523a9f..0deaad4a76a 100644
--- a/src/js/components/DataFilters/__tests__/DataFilters-test.tsx
+++ b/src/js/components/DataFilters/__tests__/DataFilters-test.tsx
@@ -1,14 +1,18 @@
 import React from 'react';
-import { render } from '@testing-library/react';
+import { act, fireEvent, render } from '@testing-library/react';
 import 'jest-styled-components';
 
 import { Data } from '../../Data';
 import { Grommet } from '../../Grommet';
 import { DataFilters } from '..';
+import { createPortal, expectPortal } from '../../../utils/portal';
 
 const data = [{ name: 'a' }, { name: 'b' }];
 
 describe('DataFilters', () => {
+  window.scrollTo = jest.fn();
+  beforeEach(createPortal);
+
   test('renders', () => {
     const { container } = render(
       <Grommet>
@@ -22,15 +26,25 @@ describe('DataFilters', () => {
   });
 
   test('drop', () => {
-    const { container } = render(
+    jest.useFakeTimers();
+
+    const { container, getByRole } = render(
       <Grommet>
-        <Data data={data}>
+        <Data id="test-data" data={data}>
           <DataFilters drop />
         </Data>
       </Grommet>,
     );
 
+    expect(getByRole('button', { name: 'Open filters' })).toBeTruthy();
     expect(container.firstChild).toMatchSnapshot();
+
+    fireEvent.click(getByRole('button', { name: 'Open filters' }));
+    // advance timers so drop can open
+    act(() => jest.advanceTimersByTime(200));
+
+    // snapshot on drop
+    expectPortal('test-data--filters-control').toMatchSnapshot();
   });
 
   test('drop badge', () => {
@@ -45,6 +59,28 @@ describe('DataFilters', () => {
     expect(container.firstChild).toMatchSnapshot();
   });
 
+  test('layer', () => {
+    jest.useFakeTimers();
+
+    const { container, getByRole } = render(
+      <Grommet>
+        <Data id="test-data" data={data}>
+          <DataFilters layer />
+        </Data>
+      </Grommet>,
+    );
+
+    expect(getByRole('button', { name: 'Open filters' })).toBeTruthy();
+    expect(container.firstChild).toMatchSnapshot();
+
+    fireEvent.click(getByRole('button', { name: 'Open filters' }));
+    // advance timers so layer can open
+    act(() => jest.advanceTimersByTime(200));
+
+    // snapshot on layer
+    expectPortal('test-data--filters-layer').toMatchSnapshot();
+  });
+
   test('properties array', () => {
     const { container } = render(
       <Grommet>
diff --git a/src/js/components/DataFilters/__tests__/__snapshots__/DataFilters-test.tsx.snap b/src/js/components/DataFilters/__tests__/__snapshots__/DataFilters-test.tsx.snap
index f24eb050446..747b5ea5781 100644
--- a/src/js/components/DataFilters/__tests__/__snapshots__/DataFilters-test.tsx.snap
+++ b/src/js/components/DataFilters/__tests__/__snapshots__/DataFilters-test.tsx.snap
@@ -687,6 +687,11 @@ exports[`DataFilters drop 1`] = `
   padding: 12px;
 }
 
+.c3:hover {
+  background-color: rgba(221,221,221,0.4);
+  color: #000000;
+}
+
 .c3:focus {
   outline: none;
   box-shadow: 0 0 2px 2px #6FFFB0;
@@ -742,7 +747,7 @@ exports[`DataFilters drop 1`] = `
 >
   <div
     class="c1"
-    id="data"
+    id="test-data"
   >
     <div
       class="c2"
@@ -750,6 +755,7 @@ exports[`DataFilters drop 1`] = `
       <button
         aria-label="Open filters"
         class="c3"
+        id="test-data--filters-control"
         type="button"
       >
         <svg
@@ -770,6 +776,80 @@ exports[`DataFilters drop 1`] = `
 </div>
 `;
 
+exports[`DataFilters drop 2`] = `
+<button
+  aria-label="Open filters"
+  class="StyledButton-sc-323bzc-0 wfYUD"
+  data-g-tabindex="none"
+  id="test-data--filters-control"
+  tabindex="-1"
+  type="button"
+>
+  <svg
+    aria-label="Filter"
+    class="StyledIcon-sc-ofa7kd-0 jQvEgu"
+    viewBox="0 0 24 24"
+  >
+    <path
+      d="m3 6 7 7v8h4v-8l7-7V3H3z"
+      fill="none"
+      stroke="#000"
+      stroke-width="2"
+    />
+  </svg>
+</button>
+`;
+
+exports[`DataFilters drop 3`] = `
+"@media only screen and (max-width: 768px) {
+  .hftPdQ {
+    margin-bottom: 6px;
+  }
+}
+@media only screen and (max-width: 768px) {
+  .zPnFT {
+    border-bottom: solid 1px rgba(0, 0, 0, 0.33);
+  }
+}
+@media only screen and (max-width: 768px) {
+  .zPnFT {
+    padding: 6px;
+  }
+}
+@media only screen and (max-width: 768px) {
+  .bGaxyQ {
+    margin-right: 6px;
+  }
+}
+@media only screen and (max-width: 768px) {
+  .dXyapD {
+    border: solid 2px rgba(0, 0, 0, 0.15);
+  }
+}
+@media only screen and (max-width: 768px) {
+  .drdMXk {
+    height: 6px;
+  }
+}
+@media only screen and (max-width: 768px) {
+  .iISQcM {
+    width: 12px;
+  }
+}
+@media only screen and (max-width: 768px) {
+  .evXIdU {
+    margin: 0px;
+  }
+}
+@media only screen and (max-width: 768px) {
+  .evXIdU {
+    font-size: 22px;
+    line-height: 28px;
+    max-width: 528px;
+  }
+}"
+`;
+
 exports[`DataFilters drop badge 1`] = `
 .c4 {
   display: inline-block;
@@ -859,6 +939,11 @@ exports[`DataFilters drop badge 1`] = `
   padding: 12px;
 }
 
+.c3:hover {
+  background-color: rgba(221,221,221,0.4);
+  color: #000000;
+}
+
 .c3:focus {
   outline: none;
   box-shadow: 0 0 2px 2px #6FFFB0;
@@ -922,6 +1007,185 @@ exports[`DataFilters drop badge 1`] = `
       <button
         aria-label="Open filters"
         class="c3"
+        id="data--filters-control"
+        type="button"
+      >
+        <svg
+          aria-label="Filter"
+          class="c4"
+          viewBox="0 0 24 24"
+        >
+          <path
+            d="m3 6 7 7v8h4v-8l7-7V3H3z"
+            fill="none"
+            stroke="#000"
+            stroke-width="2"
+          />
+        </svg>
+      </button>
+    </div>
+  </div>
+</div>
+`;
+
+exports[`DataFilters layer 1`] = `
+.c4 {
+  display: inline-block;
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+  width: 24px;
+  height: 24px;
+  fill: #666666;
+  stroke: #666666;
+}
+
+.c4 g {
+  fill: inherit;
+  stroke: inherit;
+}
+
+.c4 *:not([stroke])[fill="none"] {
+  stroke-width: 0;
+}
+
+.c4 *[stroke*="#"],
+.c4 *[STROKE*="#"] {
+  stroke: inherit;
+  fill: none;
+}
+
+.c4 *[fill-rule],
+.c4 *[FILL-RULE],
+.c4 *[fill*="#"],
+.c4 *[FILL*="#"] {
+  fill: inherit;
+  stroke: none;
+}
+
+.c1 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: column;
+  -ms-flex-direction: column;
+  flex-direction: column;
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+}
+
+.c2 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: row;
+  -ms-flex-direction: row;
+  flex-direction: row;
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+}
+
+.c3 {
+  display: inline-block;
+  box-sizing: border-box;
+  cursor: pointer;
+  font: inherit;
+  -webkit-text-decoration: none;
+  text-decoration: none;
+  margin: 0;
+  background: transparent;
+  overflow: visible;
+  text-transform: none;
+  color: inherit;
+  outline: none;
+  border: none;
+  padding: 0;
+  text-align: inherit;
+  line-height: 0;
+  padding: 12px;
+}
+
+.c3:hover {
+  background-color: rgba(221,221,221,0.4);
+  color: #000000;
+}
+
+.c3:focus {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c3:focus > circle,
+.c3:focus > ellipse,
+.c3:focus > line,
+.c3:focus > path,
+.c3:focus > polygon,
+.c3:focus > polyline,
+.c3:focus > rect {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c3:focus::-moz-focus-inner {
+  border: 0;
+}
+
+.c3:focus:not(:focus-visible) {
+  outline: none;
+  box-shadow: none;
+}
+
+.c3:focus:not(:focus-visible) > circle,
+.c3:focus:not(:focus-visible) > ellipse,
+.c3:focus:not(:focus-visible) > line,
+.c3:focus:not(:focus-visible) > path,
+.c3:focus:not(:focus-visible) > polygon,
+.c3:focus:not(:focus-visible) > polyline,
+.c3:focus:not(:focus-visible) > rect {
+  outline: none;
+  box-shadow: none;
+}
+
+.c3:focus:not(:focus-visible)::-moz-focus-inner {
+  border: 0;
+}
+
+.c0 {
+  font-size: 18px;
+  line-height: 24px;
+  box-sizing: border-box;
+  -webkit-text-size-adjust: 100%;
+  -ms-text-size-adjust: 100%;
+  -moz-osx-font-smoothing: grayscale;
+  -webkit-font-smoothing: antialiased;
+}
+
+<div
+  class="c0"
+>
+  <div
+    class="c1"
+    id="test-data"
+  >
+    <div
+      class="c2"
+    >
+      <button
+        aria-label="Open filters"
+        class="c3"
+        id="test-data--filters-control"
         type="button"
       >
         <svg
@@ -942,6 +1206,826 @@ exports[`DataFilters drop badge 1`] = `
 </div>
 `;
 
+exports[`DataFilters layer 2`] = `
+.c9 {
+  display: inline-block;
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+  width: 24px;
+  height: 24px;
+  fill: #666666;
+  stroke: #666666;
+}
+
+.c9 g {
+  fill: inherit;
+  stroke: inherit;
+}
+
+.c9 *:not([stroke])[fill="none"] {
+  stroke-width: 0;
+}
+
+.c9 *[stroke*="#"],
+.c9 *[STROKE*="#"] {
+  stroke: inherit;
+  fill: none;
+}
+
+.c9 *[fill-rule],
+.c9 *[FILL-RULE],
+.c9 *[fill*="#"],
+.c9 *[FILL*="#"] {
+  fill: inherit;
+  stroke: none;
+}
+
+.c4 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: column;
+  -ms-flex-direction: column;
+  flex-direction: column;
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+  padding: 24px;
+}
+
+.c5 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  -webkit-align-items: center;
+  -webkit-box-align: center;
+  -ms-flex-align: center;
+  align-items: center;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: row;
+  -ms-flex-direction: row;
+  flex-direction: row;
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+  -webkit-box-pack: justify;
+  -webkit-justify-content: space-between;
+  -ms-flex-pack: justify;
+  justify-content: space-between;
+}
+
+.c11 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  margin-bottom: 12px;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: column;
+  -ms-flex-direction: column;
+  flex-direction: column;
+}
+
+.c13 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  border-bottom: solid 1px rgba(0,0,0,0.33);
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: column;
+  -ms-flex-direction: column;
+  flex-direction: column;
+  padding: 12px;
+}
+
+.c14 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: column;
+  -ms-flex-direction: column;
+  flex-direction: column;
+}
+
+.c16 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  margin-right: 12px;
+  -webkit-align-items: center;
+  -webkit-box-align: center;
+  -ms-flex-align: center;
+  align-items: center;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: column;
+  -ms-flex-direction: column;
+  flex-direction: column;
+  -webkit-box-pack: center;
+  -webkit-justify-content: center;
+  -ms-flex-pack: center;
+  justify-content: center;
+}
+
+.c19 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  -webkit-align-items: center;
+  -webkit-box-align: center;
+  -ms-flex-align: center;
+  align-items: center;
+  border: solid 2px rgba(0,0,0,0.15);
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: column;
+  -ms-flex-direction: column;
+  flex-direction: column;
+  height: 24px;
+  width: 24px;
+  -webkit-box-pack: center;
+  -webkit-justify-content: center;
+  -ms-flex-pack: center;
+  justify-content: center;
+  border-radius: 4px;
+}
+
+.c7 {
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+  -webkit-align-self: stretch;
+  -ms-flex-item-align: stretch;
+  align-self: stretch;
+  width: 24px;
+}
+
+.c10 {
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+  -webkit-align-self: stretch;
+  -ms-flex-item-align: stretch;
+  align-self: stretch;
+  height: 12px;
+}
+
+.c12 {
+  margin-left: 12px;
+  margin-right: 12px;
+  margin-top: 6px;
+  margin-bottom: 6px;
+  font-size: 18px;
+  line-height: 24px;
+}
+
+.c8 {
+  display: inline-block;
+  box-sizing: border-box;
+  cursor: pointer;
+  font: inherit;
+  -webkit-text-decoration: none;
+  text-decoration: none;
+  margin: 0;
+  background: transparent;
+  overflow: visible;
+  text-transform: none;
+  color: inherit;
+  outline: none;
+  border: none;
+  padding: 0;
+  text-align: inherit;
+  line-height: 0;
+  padding: 12px;
+}
+
+.c8:hover {
+  background-color: rgba(221,221,221,0.4);
+  color: #000000;
+}
+
+.c8:focus {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c8:focus > circle,
+.c8:focus > ellipse,
+.c8:focus > line,
+.c8:focus > path,
+.c8:focus > polygon,
+.c8:focus > polyline,
+.c8:focus > rect {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c8:focus::-moz-focus-inner {
+  border: 0;
+}
+
+.c8:focus:not(:focus-visible) {
+  outline: none;
+  box-shadow: none;
+}
+
+.c8:focus:not(:focus-visible) > circle,
+.c8:focus:not(:focus-visible) > ellipse,
+.c8:focus:not(:focus-visible) > line,
+.c8:focus:not(:focus-visible) > path,
+.c8:focus:not(:focus-visible) > polygon,
+.c8:focus:not(:focus-visible) > polyline,
+.c8:focus:not(:focus-visible) > rect {
+  outline: none;
+  box-shadow: none;
+}
+
+.c8:focus:not(:focus-visible)::-moz-focus-inner {
+  border: 0;
+}
+
+.c20 {
+  display: inline-block;
+  box-sizing: border-box;
+  cursor: pointer;
+  font: inherit;
+  -webkit-text-decoration: none;
+  text-decoration: none;
+  margin: 0;
+  background: transparent;
+  overflow: visible;
+  text-transform: none;
+  border: 2px solid #7D4CDB;
+  border-radius: 18px;
+  color: #444444;
+  padding: 4px 22px;
+  font-size: 18px;
+  line-height: 24px;
+  background-color: #7D4CDB;
+  color: #f8f8f8;
+  border-radius: 18px;
+  -webkit-transition-property: color,background-color,border-color,box-shadow;
+  transition-property: color,background-color,border-color,box-shadow;
+  -webkit-transition-duration: 0.1s;
+  transition-duration: 0.1s;
+  -webkit-transition-timing-function: ease-in-out;
+  transition-timing-function: ease-in-out;
+}
+
+.c20:hover {
+  box-shadow: 0px 0px 0px 2px #7D4CDB;
+}
+
+.c20:focus {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c20:focus > circle,
+.c20:focus > ellipse,
+.c20:focus > line,
+.c20:focus > path,
+.c20:focus > polygon,
+.c20:focus > polyline,
+.c20:focus > rect {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c20:focus::-moz-focus-inner {
+  border: 0;
+}
+
+.c20:focus:not(:focus-visible) {
+  outline: none;
+  box-shadow: none;
+}
+
+.c20:focus:not(:focus-visible) > circle,
+.c20:focus:not(:focus-visible) > ellipse,
+.c20:focus:not(:focus-visible) > line,
+.c20:focus:not(:focus-visible) > path,
+.c20:focus:not(:focus-visible) > polygon,
+.c20:focus:not(:focus-visible) > polyline,
+.c20:focus:not(:focus-visible) > rect {
+  outline: none;
+  box-shadow: none;
+}
+
+.c20:focus:not(:focus-visible)::-moz-focus-inner {
+  border: 0;
+}
+
+.c21 {
+  display: inline-block;
+  box-sizing: border-box;
+  cursor: pointer;
+  font: inherit;
+  -webkit-text-decoration: none;
+  text-decoration: none;
+  margin: 0;
+  background: transparent;
+  overflow: visible;
+  text-transform: none;
+  border: 2px solid #7D4CDB;
+  border-radius: 18px;
+  color: #444444;
+  padding: 4px 22px;
+  font-size: 18px;
+  line-height: 24px;
+  opacity: 0.3;
+  cursor: default;
+  -webkit-transition-property: color,background-color,border-color,box-shadow;
+  transition-property: color,background-color,border-color,box-shadow;
+  -webkit-transition-duration: 0.1s;
+  transition-duration: 0.1s;
+  -webkit-transition-timing-function: ease-in-out;
+  transition-timing-function: ease-in-out;
+}
+
+.c21:focus {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c21:focus > circle,
+.c21:focus > ellipse,
+.c21:focus > line,
+.c21:focus > path,
+.c21:focus > polygon,
+.c21:focus > polyline,
+.c21:focus > rect {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c21:focus::-moz-focus-inner {
+  border: 0;
+}
+
+.c21:focus:not(:focus-visible) {
+  outline: none;
+  box-shadow: none;
+}
+
+.c21:focus:not(:focus-visible) > circle,
+.c21:focus:not(:focus-visible) > ellipse,
+.c21:focus:not(:focus-visible) > line,
+.c21:focus:not(:focus-visible) > path,
+.c21:focus:not(:focus-visible) > polygon,
+.c21:focus:not(:focus-visible) > polyline,
+.c21:focus:not(:focus-visible) > rect {
+  outline: none;
+  box-shadow: none;
+}
+
+.c21:focus:not(:focus-visible)::-moz-focus-inner {
+  border: 0;
+}
+
+.c15 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  -webkit-flex-direction: row;
+  -ms-flex-direction: row;
+  flex-direction: row;
+  -webkit-align-items: center;
+  -webkit-box-align: center;
+  -ms-flex-align: center;
+  align-items: center;
+  -webkit-user-select: none;
+  -moz-user-select: none;
+  -ms-user-select: none;
+  user-select: none;
+  width: -webkit-fit-content;
+  width: -moz-fit-content;
+  width: fit-content;
+  cursor: pointer;
+}
+
+.c15:hover input:not([disabled]) + div,
+.c15:hover input:not([disabled]) + span {
+  border-color: #000000;
+}
+
+.c18 {
+  opacity: 0;
+  -moz-appearance: none;
+  width: 0;
+  height: 0;
+  margin: 0;
+  cursor: pointer;
+}
+
+.c18:checked + span > span {
+  left: calc( 48px - 24px );
+  background: #7D4CDB;
+}
+
+.c17 {
+  -webkit-flex-shrink: 0;
+  -ms-flex-negative: 0;
+  flex-shrink: 0;
+}
+
+.c22 {
+  opacity: 0;
+}
+
+.c6 {
+  margin: 0px;
+  font-size: 26px;
+  line-height: 32px;
+  max-width: 624px;
+  font-weight: 600;
+  overflow-wrap: break-word;
+}
+
+.c0 {
+  font-size: 18px;
+  line-height: 24px;
+  box-sizing: border-box;
+  -webkit-text-size-adjust: 100%;
+  -ms-text-size-adjust: 100%;
+  -moz-osx-font-smoothing: grayscale;
+  -webkit-font-smoothing: antialiased;
+  background: transparent;
+  position: relative;
+  z-index: 20;
+  pointer-events: none;
+  outline: none;
+  position: fixed;
+  top: 0px;
+  left: 0px;
+  right: 0px;
+  bottom: 0px;
+}
+
+.c1 {
+  position: absolute;
+  top: 0px;
+  left: 0px;
+  right: 0px;
+  bottom: 0px;
+  background-color: rgba(0,0,0,0.5);
+  color: #f8f8f8;
+  pointer-events: all;
+  will-change: transform;
+}
+
+.c2 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  -webkit-flex-direction: column;
+  -ms-flex-direction: column;
+  flex-direction: column;
+  min-height: 48px;
+  background-color: #FFFFFF;
+  color: #444444;
+  outline: none;
+  pointer-events: all;
+  z-index: 20;
+  position: absolute;
+  max-height: calc(100% - 0px - 0px);
+  max-width: calc(100% - 0px - 0px);
+  border-radius: 0;
+  top: 0px;
+  bottom: 0px;
+  right: 0px;
+  -webkit-transform: translateX(0);
+  -ms-transform: translateX(0);
+  transform: translateX(0);
+  -webkit-animation: bKtuJK 0.2s ease-in-out forwards;
+  animation: bKtuJK 0.2s ease-in-out forwards;
+}
+
+.c3 {
+  width: 0;
+  height: 0;
+  overflow: hidden;
+  position: absolute;
+}
+
+@media only screen and (max-width:768px) {
+  .c4 {
+    padding: 12px;
+  }
+}
+
+@media only screen and (max-width:768px) {
+  .c11 {
+    margin-bottom: 6px;
+  }
+}
+
+@media only screen and (max-width:768px) {
+  .c13 {
+    border-bottom: solid 1px rgba(0,0,0,0.33);
+  }
+}
+
+@media only screen and (max-width:768px) {
+  .c13 {
+    padding: 6px;
+  }
+}
+
+@media only screen and (max-width:768px) {
+  .c16 {
+    margin-right: 6px;
+  }
+}
+
+@media only screen and (max-width:768px) {
+  .c19 {
+    border: solid 2px rgba(0,0,0,0.15);
+  }
+}
+
+@media only screen and (max-width:768px) {
+
+}
+
+@media only screen and (max-width:768px) {
+  .c7 {
+    width: 12px;
+  }
+}
+
+@media only screen and (max-width:768px) {
+  .c10 {
+    height: 6px;
+  }
+}
+
+@media only screen and (max-width:768px) {
+  .c6 {
+    margin: 0px;
+  }
+}
+
+@media only screen and (max-width:768px) {
+  .c6 {
+    font-size: 22px;
+    line-height: 28px;
+    max-width: 528px;
+  }
+}
+
+@media only screen and (max-width:768px) {
+  .c0 {
+    position: fixed;
+    width: 100%;
+    height: 100%;
+    min-height: 100vh;
+  }
+}
+
+@media only screen and (max-width:768px) {
+  .c1 {
+    position: relative;
+  }
+}
+
+@media only screen and (max-width:768px) {
+  .c2 {
+    position: relative;
+    max-height: none;
+    max-width: none;
+    border-radius: 0;
+    height: 100vh;
+    width: 100vw;
+  }
+}
+
+<div
+  class="c0"
+  id="test-data--filters-layer"
+  tabindex="-1"
+>
+  <div
+    class="c1"
+  />
+  <div
+    class="c2"
+    data-g-portal-id="0"
+    id="test-data--filters-layer"
+  >
+    <a
+      aria-hidden="true"
+      class="c3"
+      tabindex="-1"
+    />
+    <form>
+      <div
+        class="c4"
+      >
+        <header
+          class="c5"
+        >
+          <h2
+            class="c6"
+          >
+            Filters
+          </h2>
+          <div
+            class="c7"
+          />
+          <button
+            class="c8"
+            type="button"
+          >
+            <svg
+              aria-label="FormClose"
+              class="c9"
+              viewBox="0 0 24 24"
+            >
+              <path
+                d="m7 7 10 10M7 17 17 7"
+                fill="none"
+                stroke="#000"
+                stroke-width="2"
+              />
+            </svg>
+          </button>
+        </header>
+        <div
+          class="c10"
+        />
+        <div
+          class="c11 "
+        >
+          <label
+            class="c12"
+            for="test-data-name"
+          >
+            name
+          </label>
+          <div
+            class="c13 FormField__FormFieldContentBox-sc-m9hood-1"
+          >
+            <div
+              class="c14 StyledCheckBoxGroup-sc-2nhc5d-0"
+              id="test-data-name"
+              role="group"
+            >
+              <label
+                class="c15"
+                label="a"
+              >
+                <div
+                  class="c16 c17"
+                >
+                  <input
+                    class="c18"
+                    type="checkbox"
+                  />
+                  <div
+                    class="c19 "
+                  />
+                </div>
+                <span>
+                  a
+                </span>
+              </label>
+              <div
+                class="c10"
+              />
+              <label
+                class="c15"
+                label="b"
+              >
+                <div
+                  class="c16 c17"
+                >
+                  <input
+                    class="c18"
+                    type="checkbox"
+                  />
+                  <div
+                    class="c19 "
+                  />
+                </div>
+                <span>
+                  b
+                </span>
+              </label>
+            </div>
+          </div>
+        </div>
+        <div
+          class="c10"
+        />
+        <footer
+          class="c5"
+        >
+          <button
+            class="c20"
+            type="submit"
+          >
+            Apply filters
+          </button>
+          <div
+            class="c7"
+          />
+          <button
+            aria-hidden="true"
+            class="c21 c22"
+            disabled=""
+            tabindex="-1"
+            type="reset"
+          >
+            Undo changes
+          </button>
+        </footer>
+      </div>
+    </form>
+  </div>
+</div>
+`;
+
+exports[`DataFilters layer 3`] = `
+"@media only screen and (max-width: 768px) {
+  .hftPdQ {
+    margin-bottom: 6px;
+  }
+}
+@media only screen and (max-width: 768px) {
+  .zPnFT {
+    border-bottom: solid 1px rgba(0, 0, 0, 0.33);
+  }
+}
+@media only screen and (max-width: 768px) {
+  .zPnFT {
+    padding: 6px;
+  }
+}
+@media only screen and (max-width: 768px) {
+  .bGaxyQ {
+    margin-right: 6px;
+  }
+}
+@media only screen and (max-width: 768px) {
+  .dXyapD {
+    border: solid 2px rgba(0, 0, 0, 0.15);
+  }
+}
+@media only screen and (max-width: 768px) {
+  .drdMXk {
+    height: 6px;
+  }
+}
+@media only screen and (max-width: 768px) {
+  .iISQcM {
+    width: 12px;
+  }
+}
+@media only screen and (max-width: 768px) {
+  .evXIdU {
+    margin: 0px;
+  }
+}
+@media only screen and (max-width: 768px) {
+  .evXIdU {
+    font-size: 22px;
+    line-height: 28px;
+    max-width: 528px;
+  }
+}"
+`;
+
 exports[`DataFilters properties array 1`] = `
 .c1 {
   display: -webkit-box;
diff --git a/src/js/components/DataFilters/index.d.ts b/src/js/components/DataFilters/index.d.ts
index 13357dcb6c7..9d73e716fa6 100644
--- a/src/js/components/DataFilters/index.d.ts
+++ b/src/js/components/DataFilters/index.d.ts
@@ -4,6 +4,7 @@ import { BoxProps } from '../Box/index';
 export interface DataFiltersProps {
   drop?: boolean;
   heading?: string | React.ReactNode;
+  layer?: boolean;
 }
 
 type divProps = Omit<JSX.IntrinsicElements['div'], 'onClick'>;
diff --git a/src/js/components/DataFilters/propTypes.js b/src/js/components/DataFilters/propTypes.js
index 22ab3ab641d..751b7245982 100644
--- a/src/js/components/DataFilters/propTypes.js
+++ b/src/js/components/DataFilters/propTypes.js
@@ -5,6 +5,7 @@ if (process.env.NODE_ENV !== 'production') {
   PropType = {
     drop: PropTypes.bool,
     heading: PropTypes.string,
+    layer: PropTypes.bool,
   };
 }
 export const DataFiltersPropTypes = PropType;
diff --git a/src/js/components/DataFilters/stories/Layer.js b/src/js/components/DataFilters/stories/Layer.js
new file mode 100644
index 00000000000..e3510b34f38
--- /dev/null
+++ b/src/js/components/DataFilters/stories/Layer.js
@@ -0,0 +1,27 @@
+import React from 'react';
+
+import { Box, Data, DataFilter } from 'grommet';
+
+import { DataFilters } from '../DataFilters';
+import { DATA } from '../../DataTable/stories/data';
+
+export const Layer = () => (
+  // Uncomment <Grommet> lines when using outside of storybook
+  // <Grommet theme={...}>
+  <Box fill align="center" justify="start" pad="large">
+    <Data data={DATA}>
+      <DataFilters layer>
+        <DataFilter property="location" />
+      </DataFilters>
+    </Data>
+  </Box>
+  // </Grommet>
+);
+
+Layer.args = {
+  full: true,
+};
+
+export default {
+  title: 'Layout/Data/DataFilters/Layer',
+};
