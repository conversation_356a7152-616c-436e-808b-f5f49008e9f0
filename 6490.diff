diff --git a/src/js/components/Box/StyledBox.js b/src/js/components/Box/StyledBox.js
index 4f2bbbd4d63..6c2583ee72d 100644
--- a/src/js/components/Box/StyledBox.js
+++ b/src/js/components/Box/StyledBox.js
@@ -289,7 +289,7 @@ const gapStyle = (directionProp, gap, responsive, border, theme) => {
     const responsiveBorderOffset =
       responsiveBorderMetric &&
       `${
-        parseMetricToNum(responsiveMetric) / 2 -
+        parseMetricToNum(responsiveMetric || metric) / 2 -
         parseMetricToNum(responsiveBorderMetric) / 2
       }px`;
 
diff --git a/src/js/components/Box/__tests__/Box-test.tsx b/src/js/components/Box/__tests__/Box-test.tsx
index 65d8674f5a3..5d79eb1649a 100644
--- a/src/js/components/Box/__tests__/Box-test.tsx
+++ b/src/js/components/Box/__tests__/Box-test.tsx
@@ -735,6 +735,22 @@ describe('Box', () => {
     expect(container.firstChild).toMatchSnapshot();
   });
 
+  test('renders border=between and gap=pixel value', () => {
+    const { container } = render(
+      <Grommet>
+        <Box gap="12px" border="between" pad="medium">
+          <Box pad="small" background="dark-3">
+            Test 1
+          </Box>
+          <Box pad="medium" background="light-3">
+            Test 2
+          </Box>
+        </Box>
+      </Grommet>,
+    );
+    expect(container.firstChild).toMatchSnapshot();
+  });
+
   test('renders a11yTitle and aria-label', () => {
     const { container, getByLabelText } = render(
       <Grommet>
diff --git a/src/js/components/Box/__tests__/__snapshots__/Box-test.tsx.snap b/src/js/components/Box/__tests__/__snapshots__/Box-test.tsx.snap
index 1af65225d63..1f53e4f0283 100644
--- a/src/js/components/Box/__tests__/__snapshots__/Box-test.tsx.snap
+++ b/src/js/components/Box/__tests__/__snapshots__/Box-test.tsx.snap
@@ -4826,6 +4826,139 @@ exports[`Box renders a11yTitle and aria-label 1`] = `
 </div>
 `;
 
+exports[`Box renders border=between and gap=pixel value 1`] = `
+.c0 {
+  font-size: 18px;
+  line-height: 24px;
+  box-sizing: border-box;
+  -webkit-text-size-adjust: 100%;
+  -ms-text-size-adjust: 100%;
+  -moz-osx-font-smoothing: grayscale;
+  -webkit-font-smoothing: antialiased;
+}
+
+.c1 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: column;
+  -ms-flex-direction: column;
+  flex-direction: column;
+  padding: 24px;
+}
+
+.c2 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  background-color: #777777;
+  color: #f8f8f8;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: column;
+  -ms-flex-direction: column;
+  flex-direction: column;
+  padding: 12px;
+}
+
+.c4 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  background-color: #EDEDED;
+  color: #444444;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: column;
+  -ms-flex-direction: column;
+  flex-direction: column;
+  padding: 24px;
+}
+
+.c3 {
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+  -webkit-align-self: stretch;
+  -ms-flex-item-align: stretch;
+  align-self: stretch;
+  height: 12px;
+  position: relative;
+}
+
+.c3:after {
+  content: '';
+  position: absolute;
+  width: 100%;
+  top: 5.5px;
+  border-top: solid 1px rgba(0,0,0,0.33);
+}
+
+@media only screen and (max-width:768px) {
+  .c1 {
+    padding: 12px;
+  }
+}
+
+@media only screen and (max-width:768px) {
+  .c2 {
+    padding: 6px;
+  }
+}
+
+@media only screen and (max-width:768px) {
+  .c4 {
+    padding: 12px;
+  }
+}
+
+@media only screen and (max-width:768px) {
+  .c3:after {
+    border-top: solid 1px rgba(0,0,0,0.33);
+  }
+}
+
+@media only screen and (max-width:768px) {
+  .c3:after {
+    content: '';
+    top: 5.5px;
+  }
+}
+
+<div
+  class="c0"
+>
+  <div
+    class="c1"
+  >
+    <div
+      class="c2"
+    >
+      Test 1
+    </div>
+    <div
+      class="c3"
+    />
+    <div
+      class="c4"
+    >
+      Test 2
+    </div>
+  </div>
+</div>
+`;
+
 exports[`Box responsive 1`] = `
 .c0 {
   font-size: 18px;
