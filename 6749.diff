diff --git a/package.json b/package.json
index 48eca616827..74244bd3268 100644
--- a/package.json
+++ b/package.json
@@ -208,7 +208,7 @@
   "bundlesize": [
     {
       "path": "./dist/grommet.min.js",
-      "maxSize": "159 kB"
+      "maxSize": "160 kB"
     }
   ],
   "keywords": [
diff --git a/src/js/components/Data/DataForm.js b/src/js/components/Data/DataForm.js
index 2a445c1a572..b3fe40c0a5b 100644
--- a/src/js/components/Data/DataForm.js
+++ b/src/js/components/Data/DataForm.js
@@ -34,6 +34,7 @@ export const formRangeKey = '_range';
 export const formStepKey = '_step';
 export const formPageKey = '_page';
 export const formColumnsKey = '_columns';
+export const formGroupByKey = '_groupBy';
 export const formViewNameKey = '_view';
 
 const viewFormKeyMap = {
@@ -42,6 +43,7 @@ const viewFormKeyMap = {
   step: formStepKey,
   page: formPageKey,
   columns: formColumnsKey,
+  groupBy: formGroupByKey,
   view: formViewNameKey,
 };
 
@@ -110,6 +112,7 @@ const viewToFormValue = (view) => {
   if (view?.sort) result[formSortKey] = view.sort;
   if (view?.name) result[formViewNameKey] = view.name;
   if (view?.columns) result[formColumnsKey] = view.columns;
+  if (view?.groupBy) result[formGroupByKey] = view.groupBy;
 
   return unflatten(result);
 };
@@ -273,7 +276,7 @@ export const DataForm = ({
   useEffect(() => setFormValue(viewToFormValue(view)), [view]);
 
   let content = children;
-  if (footer !== false || pad) {
+  if ((footer !== false && updateOn === 'submit') || pad) {
     content = (
       <Box fill="vertical">
         <Box flex overflow="auto" pad={{ horizontal: pad, top: pad }}>
diff --git a/src/js/components/Data/stories/Inline.js b/src/js/components/Data/stories/Inline.js
index 8e94a785f93..8e93b9758d6 100644
--- a/src/js/components/Data/stories/Inline.js
+++ b/src/js/components/Data/stories/Inline.js
@@ -6,6 +6,7 @@ import {
   DataSearch,
   DataSummary,
   DataTable,
+  DataTableGroupBy,
   Grid,
   Heading,
   Notification,
@@ -18,7 +19,7 @@ const sizes = ['small', 'medium', 'large'];
 
 const DATA = [];
 
-for (let i = 0; i < 2; i += 1) {
+for (let i = 0; i < 11; i += 1) {
   DATA.push({
     id: `x-${i}`,
     sub: {
@@ -29,10 +30,18 @@ for (let i = 0; i < 2; i += 1) {
   });
 }
 
+const columns = [
+  { property: 'id', header: 'ID' },
+  { property: 'sub.name', header: 'Name' },
+  { property: 'size', header: 'Size' },
+  { property: 'date', header: 'Date' },
+];
+
 const Filters = ({ search, ...rest }) => (
   <DataFilters {...rest}>
     {search && <DataSearch property="sub.name" />}
     <DataFilter property="size" />
+    <DataTableGroupBy options={['size']} />
   </DataFilters>
 );
 
@@ -84,7 +93,7 @@ export const Inline = () => {
           {toolbar}
           <DataSummary />
           <Box flex={false}>
-            <DataTable />
+            <DataTable columns={columns} />
           </Box>
         </Box>
       </Grid>
diff --git a/src/js/components/DataTable/DataTable.js b/src/js/components/DataTable/DataTable.js
index 5153325a80f..d2b5dfb9b1d 100644
--- a/src/js/components/DataTable/DataTable.js
+++ b/src/js/components/DataTable/DataTable.js
@@ -64,7 +64,7 @@ const DataTable = ({
   data: dataProp,
   disabled,
   fill,
-  groupBy,
+  groupBy: groupByProp,
   onClickRow, // removing unknown DOM attributes
   onMore,
   onSearch, // removing unknown DOM attributes
@@ -144,6 +144,9 @@ const DataTable = ({
     else if (view?.sort) setSort(view.sort);
   }, [sortProp, view]);
 
+  // what we are grouping on
+  const groupBy = view?.groupBy || groupByProp;
+
   // the data filtered and sorted, if needed
   // Note: onUpdate mode expects the data to be passed
   //   in completely filtered and sorted already.
diff --git a/src/js/components/DataTableColumns/__tests__/__snapshots__/DataTableColumns-test.tsx.snap b/src/js/components/DataTableColumns/__tests__/__snapshots__/DataTableColumns-test.tsx.snap
index 34086be5592..cb50a8cf492 100644
--- a/src/js/components/DataTableColumns/__tests__/__snapshots__/DataTableColumns-test.tsx.snap
+++ b/src/js/components/DataTableColumns/__tests__/__snapshots__/DataTableColumns-test.tsx.snap
@@ -1,7 +1,7 @@
 // Jest Snapshot v1, https://goo.gl/fbAQLP
 
 exports[`DataTableColumns remove column 1`] = `
-.c8 {
+.c6 {
   display: inline-block;
   -webkit-flex: 0 0 auto;
   -ms-flex: 0 0 auto;
@@ -12,25 +12,25 @@ exports[`DataTableColumns remove column 1`] = `
   stroke: #666666;
 }
 
-.c8 g {
+.c6 g {
   fill: inherit;
   stroke: inherit;
 }
 
-.c8 *:not([stroke])[fill="none"] {
+.c6 *:not([stroke])[fill="none"] {
   stroke-width: 0;
 }
 
-.c8 *[stroke*="#"],
-.c8 *[STROKE*="#"] {
+.c6 *[stroke*="#"],
+.c6 *[STROKE*="#"] {
   stroke: inherit;
   fill: none;
 }
 
-.c8 *[fill-rule],
-.c8 *[FILL-RULE],
-.c8 *[fill*="#"],
-.c8 *[FILL*="#"] {
+.c6 *[fill-rule],
+.c6 *[FILL-RULE],
+.c6 *[fill*="#"],
+.c6 *[FILL*="#"] {
   fill: inherit;
   stroke: none;
 }
@@ -53,39 +53,6 @@ exports[`DataTableColumns remove column 1`] = `
 }
 
 .c3 {
-  display: -webkit-box;
-  display: -webkit-flex;
-  display: -ms-flexbox;
-  display: flex;
-  box-sizing: border-box;
-  max-width: 100%;
-  min-width: 0;
-  min-height: 0;
-  -webkit-flex-direction: column;
-  -ms-flex-direction: column;
-  flex-direction: column;
-  height: 100%;
-}
-
-.c4 {
-  display: -webkit-box;
-  display: -webkit-flex;
-  display: -ms-flexbox;
-  display: flex;
-  box-sizing: border-box;
-  max-width: 100%;
-  min-width: 0;
-  min-height: 0;
-  -webkit-flex-direction: column;
-  -ms-flex-direction: column;
-  flex-direction: column;
-  -webkit-flex: 1 1;
-  -ms-flex: 1 1;
-  flex: 1 1;
-  overflow: auto;
-}
-
-.c5 {
   display: -webkit-box;
   display: -webkit-flex;
   display: -ms-flexbox;
@@ -110,7 +77,7 @@ exports[`DataTableColumns remove column 1`] = `
   justify-content: space-between;
 }
 
-.c12 {
+.c10 {
   display: -webkit-box;
   display: -webkit-flex;
   display: -ms-flexbox;
@@ -131,7 +98,7 @@ exports[`DataTableColumns remove column 1`] = `
   justify-content: center;
 }
 
-.c16 {
+.c14 {
   display: -webkit-box;
   display: -webkit-flex;
   display: -ms-flexbox;
@@ -145,18 +112,18 @@ exports[`DataTableColumns remove column 1`] = `
   flex-direction: column;
 }
 
-.c13 {
+.c11 {
   font-size: 18px;
   line-height: 24px;
 }
 
-.c17 {
+.c15 {
   font-size: 18px;
   line-height: 24px;
   font-weight: bold;
 }
 
-.c7 {
+.c5 {
   display: inline-block;
   box-sizing: border-box;
   cursor: pointer;
@@ -176,43 +143,43 @@ exports[`DataTableColumns remove column 1`] = `
   padding: 12px;
 }
 
-.c7:focus {
+.c5:focus {
   outline: none;
   box-shadow: 0 0 2px 2px #6FFFB0;
 }
 
-.c7:focus > circle,
-.c7:focus > ellipse,
-.c7:focus > line,
-.c7:focus > path,
-.c7:focus > polygon,
-.c7:focus > polyline,
-.c7:focus > rect {
+.c5:focus > circle,
+.c5:focus > ellipse,
+.c5:focus > line,
+.c5:focus > path,
+.c5:focus > polygon,
+.c5:focus > polyline,
+.c5:focus > rect {
   outline: none;
   box-shadow: 0 0 2px 2px #6FFFB0;
 }
 
-.c7:focus::-moz-focus-inner {
+.c5:focus::-moz-focus-inner {
   border: 0;
 }
 
-.c7:focus:not(:focus-visible) {
+.c5:focus:not(:focus-visible) {
   outline: none;
   box-shadow: none;
 }
 
-.c7:focus:not(:focus-visible) > circle,
-.c7:focus:not(:focus-visible) > ellipse,
-.c7:focus:not(:focus-visible) > line,
-.c7:focus:not(:focus-visible) > path,
-.c7:focus:not(:focus-visible) > polygon,
-.c7:focus:not(:focus-visible) > polyline,
-.c7:focus:not(:focus-visible) > rect {
+.c5:focus:not(:focus-visible) > circle,
+.c5:focus:not(:focus-visible) > ellipse,
+.c5:focus:not(:focus-visible) > line,
+.c5:focus:not(:focus-visible) > path,
+.c5:focus:not(:focus-visible) > polygon,
+.c5:focus:not(:focus-visible) > polyline,
+.c5:focus:not(:focus-visible) > rect {
   outline: none;
   box-shadow: none;
 }
 
-.c7:focus:not(:focus-visible)::-moz-focus-inner {
+.c5:focus:not(:focus-visible)::-moz-focus-inner {
   border: 0;
 }
 
@@ -220,7 +187,7 @@ exports[`DataTableColumns remove column 1`] = `
   max-width: 100%;
 }
 
-.c6 {
+.c4 {
   margin: 0px;
   font-size: 26px;
   line-height: 32px;
@@ -229,7 +196,7 @@ exports[`DataTableColumns remove column 1`] = `
   overflow-wrap: break-word;
 }
 
-.c11 {
+.c9 {
   margin: 0;
   padding: 0;
   font-weight: inherit;
@@ -242,7 +209,7 @@ exports[`DataTableColumns remove column 1`] = `
   padding-bottom: 6px;
 }
 
-.c15 {
+.c13 {
   margin: 0;
   padding: 0;
   font-weight: inherit;
@@ -254,23 +221,23 @@ exports[`DataTableColumns remove column 1`] = `
   padding-bottom: 6px;
 }
 
-.c9 {
+.c7 {
   border-spacing: 0;
   border-collapse: collapse;
   width: inherit;
 }
 
-.c10 {
+.c8 {
   position: relative;
   border-spacing: 0;
   border-collapse: separate;
 }
 
-.c14:focus {
+.c12:focus {
   outline: 2px solid #6FFFB0;
 }
 
-.c14:focus:not(:focus-visible) {
+.c12:focus:not(:focus-visible) {
   outline: none;
 }
 
@@ -285,13 +252,13 @@ exports[`DataTableColumns remove column 1`] = `
 }
 
 @media only screen and (max-width:768px) {
-  .c6 {
+  .c4 {
     margin: 0px;
   }
 }
 
 @media only screen and (max-width:768px) {
-  .c6 {
+  .c4 {
     font-size: 22px;
     line-height: 28px;
     max-width: 528px;
@@ -308,49 +275,37 @@ exports[`DataTableColumns remove column 1`] = `
     <form
       class="c2"
     >
-      <div
+      <header
         class="c3"
       >
-        <div
+        <h2
           class="c4"
         >
-          <div
-            class="c1"
-          >
-            <header
-              class="c5"
-            >
-              <h2
-                class="c6"
-              >
-                Filters
-              </h2>
-            </header>
-            <button
-              aria-label="Open column selector"
-              class="c7"
-              id="test-data--columns-control"
-              type="button"
-            >
-              <svg
-                aria-label="Splits"
-                class="c8"
-                viewBox="0 0 24 24"
-              >
-                <path
-                  d="M1 22h22V2H1v20zM8 2v20V2zm8 0v20V2z"
-                  fill="none"
-                  stroke="#000"
-                  stroke-width="2"
-                />
-              </svg>
-            </button>
-          </div>
-        </div>
-      </div>
+          Filters
+        </h2>
+      </header>
+      <button
+        aria-label="Open column selector"
+        class="c5"
+        id="test-data--columns-control"
+        type="button"
+      >
+        <svg
+          aria-label="Splits"
+          class="c6"
+          viewBox="0 0 24 24"
+        >
+          <path
+            d="M1 22h22V2H1v20zM8 2v20V2zm8 0v20V2z"
+            fill="none"
+            stroke="#000"
+            stroke-width="2"
+          />
+        </svg>
+      </button>
     </form>
     <table
-      class="c9 c10"
+      class="c7 c8"
     >
       <thead
         class="StyledTable__StyledTableHeader-sc-1m3u5g-4 StyledDataTable__StyledDataTableHeader-sc-xrlyjm-4"
@@ -359,42 +314,42 @@ exports[`DataTableColumns remove column 1`] = `
           class="StyledTable__StyledTableRow-sc-1m3u5g-2 "
         >
           <th
-            class="c11 "
+            class="c9 "
             scope="col"
           >
             <div
-              class="c12"
+              class="c10"
             >
               <span
-                class="c13"
+                class="c11"
               >
                 Name
               </span>
             </div>
           </th>
           <th
-            class="c11 "
+            class="c9 "
             scope="col"
           >
             <div
-              class="c12"
+              class="c10"
             >
               <span
-                class="c13"
+                class="c11"
               >
                 Age
               </span>
             </div>
           </th>
           <th
-            class="c11 "
+            class="c9 "
             scope="col"
           >
             <div
-              class="c12"
+              class="c10"
             >
               <span
-                class="c13"
+                class="c11"
               >
                 Size
               </span>
@@ -403,40 +358,40 @@ exports[`DataTableColumns remove column 1`] = `
         </tr>
       </thead>
       <tbody
-        class="StyledTable__StyledTableBody-sc-1m3u5g-3 c14"
+        class="StyledTable__StyledTableBody-sc-1m3u5g-3 c12"
       >
         <tr
           class="StyledTable__StyledTableRow-sc-1m3u5g-2 "
         >
           <th
-            class="c15 "
+            class="c13 "
             scope="row"
           >
             <div
-              class="c16"
+              class="c14"
             >
               <span
-                class="c17"
+                class="c15"
               >
                 a
               </span>
             </div>
           </th>
           <td
-            class="c15 "
+            class="c13 "
           >
             <div
-              class="c16"
+              class="c14"
             />
           </td>
           <td
-            class="c15 "
+            class="c13 "
           >
             <div
-              class="c16"
+              class="c14"
             >
               <span
-                class="c13"
+                class="c11"
               >
                 s
               </span>
@@ -447,34 +402,34 @@ exports[`DataTableColumns remove column 1`] = `
           class="StyledTable__StyledTableRow-sc-1m3u5g-2 "
         >
           <th
-            class="c15 "
+            class="c13 "
             scope="row"
           >
             <div
-              class="c16"
+              class="c14"
             >
               <span
-                class="c17"
+                class="c15"
               >
                 b
               </span>
             </div>
           </th>
           <td
-            class="c15 "
+            class="c13 "
           >
             <div
-              class="c16"
+              class="c14"
             />
           </td>
           <td
-            class="c15 "
+            class="c13 "
           >
             <div
-              class="c16"
+              class="c14"
             >
               <span
-                class="c13"
+                class="c11"
               >
                 m
               </span>
@@ -1262,7 +1217,7 @@ exports[`DataTableColumns renders 1`] = `
 `;
 
 exports[`DataTableColumns search 1`] = `
-.c8 {
+.c6 {
   display: inline-block;
   -webkit-flex: 0 0 auto;
   -ms-flex: 0 0 auto;
@@ -1273,25 +1228,25 @@ exports[`DataTableColumns search 1`] = `
   stroke: #666666;
 }
 
-.c8 g {
+.c6 g {
   fill: inherit;
   stroke: inherit;
 }
 
-.c8 *:not([stroke])[fill="none"] {
+.c6 *:not([stroke])[fill="none"] {
   stroke-width: 0;
 }
 
-.c8 *[stroke*="#"],
-.c8 *[STROKE*="#"] {
+.c6 *[stroke*="#"],
+.c6 *[STROKE*="#"] {
   stroke: inherit;
   fill: none;
 }
 
-.c8 *[fill-rule],
-.c8 *[FILL-RULE],
-.c8 *[fill*="#"],
-.c8 *[FILL*="#"] {
+.c6 *[fill-rule],
+.c6 *[FILL-RULE],
+.c6 *[fill*="#"],
+.c6 *[FILL*="#"] {
   fill: inherit;
   stroke: none;
 }
@@ -1314,39 +1269,6 @@ exports[`DataTableColumns search 1`] = `
 }
 
 .c3 {
-  display: -webkit-box;
-  display: -webkit-flex;
-  display: -ms-flexbox;
-  display: flex;
-  box-sizing: border-box;
-  max-width: 100%;
-  min-width: 0;
-  min-height: 0;
-  -webkit-flex-direction: column;
-  -ms-flex-direction: column;
-  flex-direction: column;
-  height: 100%;
-}
-
-.c4 {
-  display: -webkit-box;
-  display: -webkit-flex;
-  display: -ms-flexbox;
-  display: flex;
-  box-sizing: border-box;
-  max-width: 100%;
-  min-width: 0;
-  min-height: 0;
-  -webkit-flex-direction: column;
-  -ms-flex-direction: column;
-  flex-direction: column;
-  -webkit-flex: 1 1;
-  -ms-flex: 1 1;
-  flex: 1 1;
-  overflow: auto;
-}
-
-.c5 {
   display: -webkit-box;
   display: -webkit-flex;
   display: -ms-flexbox;
@@ -1371,7 +1293,7 @@ exports[`DataTableColumns search 1`] = `
   justify-content: space-between;
 }
 
-.c12 {
+.c10 {
   display: -webkit-box;
   display: -webkit-flex;
   display: -ms-flexbox;
@@ -1392,7 +1314,7 @@ exports[`DataTableColumns search 1`] = `
   justify-content: center;
 }
 
-.c16 {
+.c14 {
   display: -webkit-box;
   display: -webkit-flex;
   display: -ms-flexbox;
@@ -1406,18 +1328,18 @@ exports[`DataTableColumns search 1`] = `
   flex-direction: column;
 }
 
-.c13 {
+.c11 {
   font-size: 18px;
   line-height: 24px;
 }
 
-.c17 {
+.c15 {
   font-size: 18px;
   line-height: 24px;
   font-weight: bold;
 }
 
-.c7 {
+.c5 {
   display: inline-block;
   box-sizing: border-box;
   cursor: pointer;
@@ -1437,43 +1359,43 @@ exports[`DataTableColumns search 1`] = `
   padding: 12px;
 }
 
-.c7:focus {
+.c5:focus {
   outline: none;
   box-shadow: 0 0 2px 2px #6FFFB0;
 }
 
-.c7:focus > circle,
-.c7:focus > ellipse,
-.c7:focus > line,
-.c7:focus > path,
-.c7:focus > polygon,
-.c7:focus > polyline,
-.c7:focus > rect {
+.c5:focus > circle,
+.c5:focus > ellipse,
+.c5:focus > line,
+.c5:focus > path,
+.c5:focus > polygon,
+.c5:focus > polyline,
+.c5:focus > rect {
   outline: none;
   box-shadow: 0 0 2px 2px #6FFFB0;
 }
 
-.c7:focus::-moz-focus-inner {
+.c5:focus::-moz-focus-inner {
   border: 0;
 }
 
-.c7:focus:not(:focus-visible) {
+.c5:focus:not(:focus-visible) {
   outline: none;
   box-shadow: none;
 }
 
-.c7:focus:not(:focus-visible) > circle,
-.c7:focus:not(:focus-visible) > ellipse,
-.c7:focus:not(:focus-visible) > line,
-.c7:focus:not(:focus-visible) > path,
-.c7:focus:not(:focus-visible) > polygon,
-.c7:focus:not(:focus-visible) > polyline,
-.c7:focus:not(:focus-visible) > rect {
+.c5:focus:not(:focus-visible) > circle,
+.c5:focus:not(:focus-visible) > ellipse,
+.c5:focus:not(:focus-visible) > line,
+.c5:focus:not(:focus-visible) > path,
+.c5:focus:not(:focus-visible) > polygon,
+.c5:focus:not(:focus-visible) > polyline,
+.c5:focus:not(:focus-visible) > rect {
   outline: none;
   box-shadow: none;
 }
 
-.c7:focus:not(:focus-visible)::-moz-focus-inner {
+.c5:focus:not(:focus-visible)::-moz-focus-inner {
   border: 0;
 }
 
@@ -1481,7 +1403,7 @@ exports[`DataTableColumns search 1`] = `
   max-width: 100%;
 }
 
-.c6 {
+.c4 {
   margin: 0px;
   font-size: 26px;
   line-height: 32px;
@@ -1490,7 +1412,7 @@ exports[`DataTableColumns search 1`] = `
   overflow-wrap: break-word;
 }
 
-.c11 {
+.c9 {
   margin: 0;
   padding: 0;
   font-weight: inherit;
@@ -1503,7 +1425,7 @@ exports[`DataTableColumns search 1`] = `
   padding-bottom: 6px;
 }
 
-.c15 {
+.c13 {
   margin: 0;
   padding: 0;
   font-weight: inherit;
@@ -1515,23 +1437,23 @@ exports[`DataTableColumns search 1`] = `
   padding-bottom: 6px;
 }
 
-.c9 {
+.c7 {
   border-spacing: 0;
   border-collapse: collapse;
   width: inherit;
 }
 
-.c10 {
+.c8 {
   position: relative;
   border-spacing: 0;
   border-collapse: separate;
 }
 
-.c14:focus {
+.c12:focus {
   outline: 2px solid #6FFFB0;
 }
 
-.c14:focus:not(:focus-visible) {
+.c12:focus:not(:focus-visible) {
   outline: none;
 }
 
@@ -1546,13 +1468,13 @@ exports[`DataTableColumns search 1`] = `
 }
 
 @media only screen and (max-width:768px) {
-  .c6 {
+  .c4 {
     margin: 0px;
   }
 }
 
 @media only screen and (max-width:768px) {
-  .c6 {
+  .c4 {
     font-size: 22px;
     line-height: 28px;
     max-width: 528px;
@@ -1569,49 +1491,37 @@ exports[`DataTableColumns search 1`] = `
     <form
       class="c2"
     >
-      <div
+      <header
         class="c3"
       >
-        <div
+        <h2
           class="c4"
         >
-          <div
-            class="c1"
-          >
-            <header
-              class="c5"
-            >
-              <h2
-                class="c6"
-              >
-                Filters
-              </h2>
-            </header>
-            <button
-              aria-label="Open column selector"
-              class="c7"
-              id="test-data--columns-control"
-              type="button"
-            >
-              <svg
-                aria-label="Splits"
-                class="c8"
-                viewBox="0 0 24 24"
-              >
-                <path
-                  d="M1 22h22V2H1v20zM8 2v20V2zm8 0v20V2z"
-                  fill="none"
-                  stroke="#000"
-                  stroke-width="2"
-                />
-              </svg>
-            </button>
-          </div>
-        </div>
-      </div>
+          Filters
+        </h2>
+      </header>
+      <button
+        aria-label="Open column selector"
+        class="c5"
+        id="test-data--columns-control"
+        type="button"
+      >
+        <svg
+          aria-label="Splits"
+          class="c6"
+          viewBox="0 0 24 24"
+        >
+          <path
+            d="M1 22h22V2H1v20zM8 2v20V2zm8 0v20V2z"
+            fill="none"
+            stroke="#000"
+            stroke-width="2"
+          />
+        </svg>
+      </button>
     </form>
     <table
-      class="c9 c10"
+      class="c7 c8"
     >
       <thead
         class="StyledTable__StyledTableHeader-sc-1m3u5g-4 StyledDataTable__StyledDataTableHeader-sc-xrlyjm-4"
@@ -1620,28 +1530,28 @@ exports[`DataTableColumns search 1`] = `
           class="StyledTable__StyledTableRow-sc-1m3u5g-2 "
         >
           <th
-            class="c11 "
+            class="c9 "
             scope="col"
           >
             <div
-              class="c12"
+              class="c10"
             >
               <span
-                class="c13"
+                class="c11"
               >
                 Name
               </span>
             </div>
           </th>
           <th
-            class="c11 "
+            class="c9 "
             scope="col"
           >
             <div
-              class="c12"
+              class="c10"
             >
               <span
-                class="c13"
+                class="c11"
               >
                 Size
               </span>
@@ -1650,33 +1560,33 @@ exports[`DataTableColumns search 1`] = `
         </tr>
       </thead>
       <tbody
-        class="StyledTable__StyledTableBody-sc-1m3u5g-3 c14"
+        class="StyledTable__StyledTableBody-sc-1m3u5g-3 c12"
       >
         <tr
           class="StyledTable__StyledTableRow-sc-1m3u5g-2 "
         >
           <th
-            class="c15 "
+            class="c13 "
             scope="row"
           >
             <div
-              class="c16"
+              class="c14"
             >
               <span
-                class="c17"
+                class="c15"
               >
                 a
               </span>
             </div>
           </th>
           <td
-            class="c15 "
+            class="c13 "
           >
             <div
-              class="c16"
+              class="c14"
             >
               <span
-                class="c13"
+                class="c11"
               >
                 s
               </span>
@@ -1687,27 +1597,27 @@ exports[`DataTableColumns search 1`] = `
           class="StyledTable__StyledTableRow-sc-1m3u5g-2 "
         >
           <th
-            class="c15 "
+            class="c13 "
             scope="row"
           >
             <div
-              class="c16"
+              class="c14"
             >
               <span
-                class="c17"
+                class="c15"
               >
                 b
               </span>
             </div>
           </th>
           <td
-            class="c15 "
+            class="c13 "
           >
             <div
-              class="c16"
+              class="c14"
             >
               <span
-                class="c13"
+                class="c11"
               >
                 m
               </span>
diff --git a/src/js/components/DataTableGroupBy/DataTableGroupBy.js b/src/js/components/DataTableGroupBy/DataTableGroupBy.js
new file mode 100644
index 00000000000..9577f9330a4
--- /dev/null
+++ b/src/js/components/DataTableGroupBy/DataTableGroupBy.js
@@ -0,0 +1,69 @@
+import React, { useContext, useEffect } from 'react';
+import { DataForm } from '../Data/DataForm';
+import { DataContext } from '../../contexts/DataContext';
+import { FormContext } from '../Form/FormContext';
+import { FormField } from '../FormField';
+import { Select } from '../Select';
+import { MessageContext } from '../../contexts/MessageContext';
+import { DataTableGroupByPropTypes } from './propTypes';
+
+export const DataTableGroupBy = ({ id: idProp, options, ...rest }) => {
+  const { id: dataId, messages, view, addToolbarKey } = useContext(DataContext);
+  const { noForm } = useContext(FormContext);
+  const { format } = useContext(MessageContext);
+  const id = idProp || `${dataId}--groupby`;
+
+  useEffect(() => {
+    if (noForm) addToolbarKey('_groupBy');
+  }, [addToolbarKey, noForm]);
+
+  if (!options) return null;
+
+  let content = (
+    <Select
+      id={id}
+      name="_groupBy"
+      showSelectedInline
+      placeholder={noForm ? 'Group by' : undefined}
+      options={options}
+      labelKey="label"
+      clear={
+        view?.groupBy
+          ? {
+              position: 'top',
+              label: format({
+                id: 'dataTableGroupBy.clear',
+                messages: messages?.dataTableGroupBy,
+              }),
+            }
+          : undefined
+      }
+      value={view?.groupBy}
+      {...rest}
+    />
+  );
+
+  if (noForm)
+    // likely in Toolbar
+    content = (
+      <DataForm footer={false} updateOn="change">
+        {content}
+      </DataForm>
+    );
+  else
+    content = (
+      <FormField
+        htmlFor={id}
+        label={format({
+          id: 'dataTableGroupBy.label',
+          messages: messages?.dataTableGroupBy,
+        })}
+      >
+        {content}
+      </FormField>
+    );
+
+  return content;
+};
+
+DataTableGroupBy.propTypes = DataTableGroupByPropTypes;
diff --git a/src/js/components/DataTableGroupBy/README.md b/src/js/components/DataTableGroupBy/README.md
new file mode 100644
index 00000000000..658ea5532d5
--- /dev/null
+++ b/src/js/components/DataTableGroupBy/README.md
@@ -0,0 +1,2 @@
+## DataTableGroupBy
+Documentation for this component: https://v2.grommet.io/datatablegroupby
\ No newline at end of file
diff --git a/src/js/components/DataTableGroupBy/__tests__/DataTableGroupBy-test.tsx b/src/js/components/DataTableGroupBy/__tests__/DataTableGroupBy-test.tsx
new file mode 100644
index 00000000000..3d002881102
--- /dev/null
+++ b/src/js/components/DataTableGroupBy/__tests__/DataTableGroupBy-test.tsx
@@ -0,0 +1,29 @@
+import React from 'react';
+import { render } from '@testing-library/react';
+import 'jest-styled-components';
+
+import { Data } from '../../Data';
+import { Grommet } from '../../Grommet';
+import { DataTableGroupBy } from '..';
+
+const data = [
+  { name: 'aa', enabled: true, rating: 2.3, type: { name: 'ZZ', id: 1 } },
+  { name: 'bb', enabled: false, rating: 4.3, type: { name: 'YY', id: 2 } },
+  { name: 'cc', type: { name: 'ZZ', id: 1 } },
+];
+
+describe('DataTableGroupBy', () => {
+  test('renders', () => {
+    const { container } = render(
+      <Grommet>
+        <Data data={data}>
+          <DataTableGroupBy
+            options={[{ property: 'type.name', label: 'Type' }]}
+          />
+        </Data>
+      </Grommet>,
+    );
+
+    expect(container.firstChild).toMatchSnapshot();
+  });
+});
diff --git a/src/js/components/DataTableGroupBy/__tests__/__snapshots__/DataTableGroupBy-test.tsx.snap b/src/js/components/DataTableGroupBy/__tests__/__snapshots__/DataTableGroupBy-test.tsx.snap
new file mode 100644
index 00000000000..a51ec424893
--- /dev/null
+++ b/src/js/components/DataTableGroupBy/__tests__/__snapshots__/DataTableGroupBy-test.tsx.snap
@@ -0,0 +1,314 @@
+// Jest Snapshot v1, https://goo.gl/fbAQLP
+
+exports[`DataTableGroupBy renders 1`] = `
+.c11 {
+  display: inline-block;
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+  width: 24px;
+  height: 24px;
+  fill: #7D4CDB;
+  stroke: #7D4CDB;
+}
+
+.c11 g {
+  fill: inherit;
+  stroke: inherit;
+}
+
+.c11 *:not([stroke])[fill="none"] {
+  stroke-width: 0;
+}
+
+.c11 *[stroke*="#"],
+.c11 *[STROKE*="#"] {
+  stroke: inherit;
+  fill: none;
+}
+
+.c11 *[fill-rule],
+.c11 *[FILL-RULE],
+.c11 *[fill*="#"],
+.c11 *[FILL*="#"] {
+  fill: inherit;
+  stroke: none;
+}
+
+.c1 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: column;
+  -ms-flex-direction: column;
+  flex-direction: column;
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+}
+
+.c5 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  -webkit-align-items: center;
+  -webkit-box-align: center;
+  -ms-flex-align: center;
+  align-items: center;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: row;
+  -ms-flex-direction: row;
+  flex-direction: row;
+  -webkit-box-pack: justify;
+  -webkit-justify-content: space-between;
+  -ms-flex-pack: justify;
+  justify-content: space-between;
+}
+
+.c6 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: row;
+  -ms-flex-direction: row;
+  flex-direction: row;
+  -webkit-flex: 1 1;
+  -ms-flex: 1 1;
+  flex: 1 1;
+  -webkit-flex-basis: auto;
+  -ms-flex-preferred-size: auto;
+  flex-basis: auto;
+}
+
+.c10 {
+  display: -webkit-box;
+  display: -webkit-flex;
+  display: -ms-flexbox;
+  display: flex;
+  box-sizing: border-box;
+  max-width: 100%;
+  margin-left: 12px;
+  margin-right: 12px;
+  min-width: 0;
+  min-height: 0;
+  -webkit-flex-direction: column;
+  -ms-flex-direction: column;
+  flex-direction: column;
+  -webkit-flex: 0 0 auto;
+  -ms-flex: 0 0 auto;
+  flex: 0 0 auto;
+}
+
+.c3 {
+  display: inline-block;
+  box-sizing: border-box;
+  cursor: pointer;
+  font: inherit;
+  -webkit-text-decoration: none;
+  text-decoration: none;
+  margin: 0;
+  background: transparent;
+  overflow: visible;
+  text-transform: none;
+  color: inherit;
+  outline: none;
+  border: none;
+  padding: 0;
+  text-align: inherit;
+}
+
+.c3:focus {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c3:focus > circle,
+.c3:focus > ellipse,
+.c3:focus > line,
+.c3:focus > path,
+.c3:focus > polygon,
+.c3:focus > polyline,
+.c3:focus > rect {
+  outline: none;
+  box-shadow: 0 0 2px 2px #6FFFB0;
+}
+
+.c3:focus::-moz-focus-inner {
+  border: 0;
+}
+
+.c3:focus:not(:focus-visible) {
+  outline: none;
+  box-shadow: none;
+}
+
+.c3:focus:not(:focus-visible) > circle,
+.c3:focus:not(:focus-visible) > ellipse,
+.c3:focus:not(:focus-visible) > line,
+.c3:focus:not(:focus-visible) > path,
+.c3:focus:not(:focus-visible) > polygon,
+.c3:focus:not(:focus-visible) > polyline,
+.c3:focus:not(:focus-visible) > rect {
+  outline: none;
+  box-shadow: none;
+}
+
+.c3:focus:not(:focus-visible)::-moz-focus-inner {
+  border: 0;
+}
+
+.c8 {
+  box-sizing: border-box;
+  font-size: inherit;
+  font-family: inherit;
+  border: none;
+  -webkit-appearance: none;
+  background: transparent;
+  color: inherit;
+  width: 100%;
+  padding: 11px;
+  font-weight: 600;
+  margin: 0;
+  border: 1px solid rgba(0,0,0,0.33);
+  border-radius: 4px;
+  outline: none;
+  border: none;
+}
+
+.c8::-webkit-input-placeholder {
+  color: #AAAAAA;
+}
+
+.c8::-moz-placeholder {
+  color: #AAAAAA;
+}
+
+.c8:-ms-input-placeholder {
+  color: #AAAAAA;
+}
+
+.c8::-webkit-search-decoration {
+  -webkit-appearance: none;
+}
+
+.c8::-moz-focus-inner {
+  border: none;
+  outline: none;
+}
+
+.c8:-moz-placeholder,
+.c8::-moz-placeholder {
+  opacity: 1;
+}
+
+.c7 {
+  position: relative;
+  width: 100%;
+}
+
+.c9 {
+  cursor: pointer;
+}
+
+.c4 {
+  border: 1px solid rgba(0,0,0,0.33);
+  border-radius: 4px;
+}
+
+.c2 {
+  max-width: 100%;
+}
+
+.c0 {
+  font-size: 18px;
+  line-height: 24px;
+  box-sizing: border-box;
+  -webkit-text-size-adjust: 100%;
+  -ms-text-size-adjust: 100%;
+  -moz-osx-font-smoothing: grayscale;
+  -webkit-font-smoothing: antialiased;
+}
+
+@media only screen and (max-width:768px) {
+  .c10 {
+    margin-left: 6px;
+    margin-right: 6px;
+  }
+}
+
+<div
+  class="c0"
+>
+  <div
+    class="c1"
+    id="data"
+  >
+    <form
+      class="c2"
+    >
+      <button
+        aria-expanded="false"
+        aria-haspopup="listbox"
+        aria-label="Group by"
+        class="c3 c4"
+        id="data--groupby"
+        type="button"
+      >
+        <div
+          class="c5"
+        >
+          <div
+            class="c6"
+          >
+            <div
+              class="c7"
+            >
+              <input
+                autocomplete="off"
+                class="c8 c9"
+                id="data--groupby__input"
+                name="_groupBy"
+                placeholder="Group by"
+                readonly=""
+                tabindex="-1"
+                type="text"
+                value=""
+              />
+            </div>
+          </div>
+          <div
+            class="c10"
+            style="min-width: auto;"
+          >
+            <svg
+              aria-label="FormDown"
+              class="c11"
+              viewBox="0 0 24 24"
+            >
+              <path
+                d="m18 9-6 6-6-6"
+                fill="none"
+                stroke="#000"
+                stroke-width="2"
+              />
+            </svg>
+          </div>
+        </div>
+      </button>
+    </form>
+  </div>
+</div>
+`;
diff --git a/src/js/components/DataTableGroupBy/index.d.ts b/src/js/components/DataTableGroupBy/index.d.ts
new file mode 100644
index 00000000000..87e2988ff46
--- /dev/null
+++ b/src/js/components/DataTableGroupBy/index.d.ts
@@ -0,0 +1,20 @@
+import * as React from 'react';
+import { BoxProps } from '../Box/index';
+
+export interface DataTableGroupByProps {
+  options: string[] | { label: string; property: string }[];
+}
+
+type selectProps = Omit<
+  JSX.IntrinsicElements['select'],
+  'onClick' | 'property'
+>;
+
+export interface DataTableGroupByExtendedProps
+  extends BoxProps,
+    DataTableGroupByProps,
+    selectProps {}
+
+declare const DataTableGroupBy: React.FC<DataTableGroupByExtendedProps>;
+
+export { DataTableGroupBy };
diff --git a/src/js/components/DataTableGroupBy/index.js b/src/js/components/DataTableGroupBy/index.js
new file mode 100644
index 00000000000..4a6db2fcb06
--- /dev/null
+++ b/src/js/components/DataTableGroupBy/index.js
@@ -0,0 +1 @@
+export { DataTableGroupBy } from './DataTableGroupBy';
diff --git a/src/js/components/DataTableGroupBy/propTypes.js b/src/js/components/DataTableGroupBy/propTypes.js
new file mode 100644
index 00000000000..2921f5bdd34
--- /dev/null
+++ b/src/js/components/DataTableGroupBy/propTypes.js
@@ -0,0 +1,15 @@
+import PropTypes from 'prop-types';
+
+let PropType = {};
+if (process.env.NODE_ENV !== 'production') {
+  PropType = {
+    options: PropTypes.oneOfType([
+      PropTypes.arrayOf(PropTypes.string),
+      PropTypes.arrayOf(PropTypes.shape({
+        label: PropTypes.string,
+        property: PropTypes.string,
+      })),
+    ]),
+  };
+}
+export const DataTableGroupByPropTypes = PropType;
diff --git a/src/js/components/DataTableGroupBy/stories/Simple.js b/src/js/components/DataTableGroupBy/stories/Simple.js
new file mode 100644
index 00000000000..d4ba1f33c4a
--- /dev/null
+++ b/src/js/components/DataTableGroupBy/stories/Simple.js
@@ -0,0 +1,37 @@
+import React from 'react';
+
+import { Box, Data, DataTable, Notification, Toolbar } from 'grommet';
+
+import { DataTableGroupBy } from '../DataTableGroupBy';
+import { columns, DATA } from '../../DataTable/stories/data';
+
+// simplify option label for name property
+const options = columns
+  .filter(({ property }) => ['location', 'percent'].includes(property))
+  .map(({ header, property }) => ({ property, label: header }));
+
+export const Simple = () => (
+  // Uncomment <Grommet> lines when using outside of storybook
+  // <Grommet theme={...}>
+  <Box align="center" justify="start" pad="large" gap="medium">
+    <Notification
+      status="info"
+      message="Data is in 'beta'. The API surface is subject to change."
+    />
+    <Data data={DATA}>
+      <Toolbar>
+        <DataTableGroupBy options={options} />
+      </Toolbar>
+      <DataTable />
+    </Data>
+  </Box>
+  // </Grommet>
+);
+
+Simple.args = {
+  full: true,
+};
+
+export default {
+  title: 'Data/DataTableGroupBy/Simple',
+};
diff --git a/src/js/components/__tests__/__snapshots__/components-test.js.snap b/src/js/components/__tests__/__snapshots__/components-test.js.snap
index d898d919552..51808e590b2 100644
--- a/src/js/components/__tests__/__snapshots__/components-test.js.snap
+++ b/src/js/components/__tests__/__snapshots__/components-test.js.snap
@@ -291,6 +291,7 @@ exports[`Components loads 1`] = `
   "DataSummary": [Function],
   "DataTable": [Function],
   "DataTableColumns": [Function],
+  "DataTableGroupBy": [Function],
   "DataView": [Function],
   "DateInput": {
     "$$typeof": Symbol(react.forward_ref),
diff --git a/src/js/components/index.d.ts b/src/js/components/index.d.ts
index 2be422b605b..a9e1dd5b745 100644
--- a/src/js/components/index.d.ts
+++ b/src/js/components/index.d.ts
@@ -25,6 +25,7 @@ export * from './DataSort';
 export * from './DataSummary';
 export * from './DataTable';
 export * from './DataTableColumns';
+export * from './DataTableGroupBy';
 export * from './DataView';
 export * from './DateInput';
 export * from './Diagram';
diff --git a/src/js/components/index.js b/src/js/components/index.js
index 6ec3242651c..2b8497b53eb 100644
--- a/src/js/components/index.js
+++ b/src/js/components/index.js
@@ -25,6 +25,7 @@ export * from './DataSort';
 export * from './DataSummary';
 export * from './DataTable';
 export * from './DataTableColumns';
+export * from './DataTableGroupBy';
 export * from './DataView';
 export * from './DateInput';
 export * from './Diagram';
diff --git a/src/js/languages/default.json b/src/js/languages/default.json
index 4b20dad75ba..632c61a3f88 100644
--- a/src/js/languages/default.json
+++ b/src/js/languages/default.json
@@ -53,6 +53,10 @@
     "order": "Order columns",
     "select": "Select columns"
   },
+  "dataTableGroupBy": {
+    "clear": "Clear group",
+    "label": "Group by"
+  },
   "dataView": {
     "label": "View"
   },
